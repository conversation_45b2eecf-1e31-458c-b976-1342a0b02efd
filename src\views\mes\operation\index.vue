<template>
  <div class="app-container">
      <div class="app-container-div">
     <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="工序有效时间" prop="processValidityPeriod">
        <el-input
          v-model="queryParams.processValidityPeriod"
          placeholder="请输入工序有效时间"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input
          v-model="queryParams.sort"
          placeholder="请输入排序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="排列顺序" prop="sortNumber">
        <el-input
          v-model="queryParams.sortNumber"
          placeholder="请输入排列顺序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参与APS排程" prop="apsStr">
        <el-input
          v-model="queryParams.apsStr"
          placeholder="请输入参与APS排程"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否参与APS排程" prop="apsEnable">
        <el-input
          v-model="queryParams.apsEnable"
          placeholder="请输入是否参与APS排程"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属工厂" prop="siteId">
        <el-input
          v-model="queryParams.siteId"
          placeholder="请输入所属工厂"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="工序编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入工序编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工序名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入工序名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="描述" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="节拍" prop="cycleTime">
        <el-input
          v-model="queryParams.cycleTime"
          placeholder="请输入节拍"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mes:operation:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          size="mini"
          @click="handleBatchSort"
          v-hasPermi="['mes:operation:sort']"
        >批量排序</el-button>
      </el-col> 
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mes:operation:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mes:operation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mes:operation:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="operationList" 
    @row-click="handleRowClick"
    @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="参数校验" align="center" prop="parameCheck" />
      <el-table-column label="工序有效时间" align="center" prop="processValidityPeriod" />
      <el-table-column label="排序" align="center" prop="sort" /> 
     <el-table-column label="参与APS排程" align="center" prop="apsStr" />
      <el-table-column label="是否参与APS排程" align="center" prop="apsEnable" /> 
      <el-table-column label="工序类型" align="center" prop="operationType" /> -->
      <el-table-column label="工序编码" align="center" prop="code" />
      <el-table-column label="工序名称" align="center" prop="name" />
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="节拍" align="center" prop="cycleTime" />
      <el-table-column label="修改时间" align="center" prop="updateTime"/>
       <el-table-column label="所属工厂" align="center" prop="siteId" />
      <!-- <el-table-column label="排列顺序" align="center" prop="sortNumber" /> -->
      <el-table-column label="排列顺序" align="center" prop="sortNumber">
        <template slot-scope="scope">
          <!-- 显示当前排列顺序值 -->
          <span>{{ scope.row.sortNumber }}</span> 
          <!-- 编辑图标，点击触发打开小弹窗 -->
          <el-tooltip content="编辑排列顺序" placement="top">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="mini"
              @click="openSortDialog(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mes:operation:edit']"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mes:operation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工序对话框 -->
          <el-dialog :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <!-- <el-form-item label="参数校验" prop="parameCheck" style="width: 700px;">
          <el-input v-model="form.parameCheck" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="工序有效时间" prop="processValidityPeriod" style="width: 240px;">
          <el-input v-model="form.processValidityPeriod" placeholder="请输入工序有效时间" />
        </el-form-item>
        <el-form-item label="排序" prop="sort" style="width: 240px;">
          <el-input v-model="form.sort" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="排列顺序" prop="sortNumber" style="width: 240px;">
          <el-input v-model="form.sortNumber" placeholder="请输入排列顺序" />
        </el-form-item>
        <el-form-item label="参与APS排程" prop="apsStr" style="width: 240px;">
          <el-input v-model="form.apsStr" placeholder="请输入参与APS排程" />
        </el-form-item>
        <el-form-item label="是否参与APS排程" prop="apsEnable" style="width: 240px;">
          <el-input v-model="form.apsEnable" placeholder="请输入是否参与APS排程" />
        </el-form-item>
         -->
        <el-form-item label="工序编码" prop="code" style="width: 240px;">
          <el-input v-model="form.code" placeholder="请输入工序编码" />
        </el-form-item>
        <el-form-item label="工序名称" prop="name" style="width: 240px;">
          <el-input v-model="form.name" placeholder="请输入工序名称" />
        </el-form-item> 
        <el-form-item label="节拍" prop="cycleTime" style="width: 240px;">
          <el-input v-model="form.cycleTime" placeholder="请输入节拍" />
        </el-form-item>
        <el-form-item label="所属工厂" prop="siteId" style="width: 240px;">
          <el-input v-model="form.siteId" placeholder="请输入所属工厂" />
        </el-form-item>
        <el-form-item label="描述" prop="description" style="width: 240px;">
          <el-input v-model="form.description" placeholder="请输入描述" />
        </el-form-item>
      </el-form>
              <div class="el-dialog__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 排列顺序编辑小弹窗 -->
    <el-dialog
      title="编辑排列顺序"
      :visible.sync="sortDialogVisible"
      width="30%"
      append-to-body
    >
      <el-form
        ref="sortFormRef"
        :model="sortForm"
        :rules="sortRules"
        label-width="100px"
      >
        <el-form-item label="排列顺序" prop="sortNumber">
          <el-input
            v-model="sortForm.sortNumber"
            placeholder="请输入排列顺序"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="sortDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveSort">确定</el-button>
      </div>
    </el-dialog>

    <!-- 批量排序弹窗 -->
    <el-dialog
      title="批量排序"
      :visible.sync="openSort"
      size="50%"
      :fullscreen="isBatchFullscreen"
      append-to-body
      custom-class="batch-sort-dialog"
    >
     <!-- 自定义标题栏（添加全屏按钮） -->
      <template slot="title">
        <span>批量排序</span>
        <el-button
          :icon="isBatchFullscreen ? 'el-icon-close' : 'el-icon-full-screen'"
          type="text"
          style="float: right; margin-top: -5px; margin-right: 30px;"
          @click="toggleBatchFullscreen"
        ></el-button>
      </template>
      <!-- 表单部分 -->
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="工序编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入工序编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工序名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入工序名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
<!-- 表格部分 -->
<div class="dialog-content">
<el-table :height="batchTableHeight" v-loading="loading" :data="operationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" /> 
      <!-- <el-table-column label="是否参与APS排程" align="center" prop="apsEnable" />  --> 
      <el-table-column label="工序编码" align="center" prop="code" />
      <el-table-column label="工序名称" align="center" prop="name" />
       <el-table-column label="工序类型" align="center" prop="operationType" /> 
      <el-table-column label="描述" align="center" prop="description" />
      <el-table-column label="节拍" align="center" prop="cycleTime" />
      <el-table-column label="修改时间" align="center" prop="updateTime"/>
       <el-table-column label="所属工厂" align="center" prop="siteId" />
       <el-table-column label="参与APS排程" align="center" prop="apsStr" />
      <el-table-column label="排列顺序" align="center" prop="sortNumber">
        <template slot-scope="scope">
          <!-- 显示当前排列顺序值 -->
          <span>{{ scope.row.sortNumber }}</span> 
          <!-- 编辑图标，点击触发打开小弹窗 -->
          <el-tooltip content="编辑排列顺序" placement="top">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="mini"
              @click="openSortDialog(scope.row)"
            ></el-button>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>
  </div>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

      <div class="el-dialog__footer">      
        <el-button type="warning" @click="cancel">退出</el-button>
         <el-button type="primary" @click="submitform">保存并退出</el-button>
      </div>
    </el-dialog>

    <!-- ------------------------工序属性------------------------- -->
<el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd2"
          v-hasPermi="['mes:parameter:add']"
        >新增设备</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList2"></right-toolbar>
      </el-row>
<el-table height="62vh" v-loading="loadingParameter" :data="parameterList" @selection-change="handleSelectionChange2">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="设备名称" align="center" prop="equipName" />
      <el-table-column label="工位编号" align="center" prop="equipCode" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate2(scope.row)"
            v-hasPermi="['mes:parameter:edit']"
          >编辑</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete2(scope.row)"
            v-hasPermi="['mes:parameter:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList2"
    />
    <!-- 添加或修改工序属性对话框 -->
          <el-dialog :title="title2" :visible.sync="open2" :size="'50%'" append-to-body>
      <el-form ref="form2" :model="form2" :rules="rules2" >
        <el-form-item label="设备名称" prop="equipName" style="width: 240px;">
          <el-input v-model="form2.equipName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备编号" prop="equipCode" style="width: 240px;">
          <el-input v-model="form2.equipCode" placeholder="请输入设备编号" />
        </el-form-item>
      </el-form>
              <div class="el-dialog__footer">
        <el-button type="primary" @click="submitForm2">确 定</el-button>
        <el-button @click="cancel2">取 消</el-button>
      </div>
    </el-dialog>
      </div>
  </div>
</template>

<script>
import { listOperation, getOperation, delOperation, addOperation, updateOperation } from "@/api/mes/operation";
import { listParameter, getParameter, delParameter, addParameter, updateParameter } from "@/api/mes/parameter";
export default {
  name: "Operation",
  data() {
    return {
      // 遮罩层
      loading: true,
      loadingParameter: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工序表格数据
      operationList: [],
      // 工序属性表格数据
      parameterList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      open2: false,
      openSort: false,
      // 批量排序弹窗全屏状态
      isBatchFullscreen: false,

      // 排列顺序小弹窗相关
      // 控制小弹窗显示隐藏
      sortDialogVisible: false, 
      // 存储当前点击编辑的表格行数据
      currentSortRow: null, 
       sortForm: {
      sortNumber: ""
    },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        parameCheck: null,
        processValidityPeriod: null,
        sort: null,
        sortNumber: null,
        apsStr: null,
        apsEnable: null,
        siteId: null,
        operationType: null,
        code: null,
        name: null,
        description: null,
        cycleTime: null,
        updateTime: null,
      },
      params: {
        workingProduceId: null,
      },
      // 表单参数
      form: {},
      form2: {},
      // 表单校验
      rules: {
        code: [
          { required: true, message: "工序编码不能为空", trigger: "blur" }
        ],
        name: [
          { required: true, message: "工序名称不能为空", trigger: "blur" }
        ],
        siteId: [
          { required: true, message: "所属工厂不能为空", trigger: "blur" }
        ],
      },
      rules2: {
        equipCode: [
          { required: true, message: "设备编号不能为空", trigger: "blur" }
        ],
        equipName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
      }
    };
  },
  computed: {
    // 动态计算表格高度（全屏时更高）
    batchTableHeight() {
      return this.isBatchFullscreen ? 'calc(100vh - 280px)' : '62vh';
    }
  },
  created() {
    this.getList();
  },
  methods: {
    // 行点击
    handleRowClick(row) {
      console.log(row.id);
      this.getListParameters(row.id);
    },
    // 查询工序属性
getListParameters(id) {
      this.params.workingProduceId=id;
      this.loadingParameter = true;
      listParameter(this.params).then((response) => {
        this.parameterList = response.rows;
        // this.total = response.total;
        this.loadingParameter = false;
      });
    },
   // 打开小弹窗
    openSortDialog(row) {
      // 记录当前行
    this.currentSortRow = row; 
    // 回显当前排列顺序值
    this.sortForm.sortNumber = row.sortNumber; 
    // 打开小弹窗
    this.sortDialogVisible = true; 
  },

  // 保存排列顺序
  saveSort() {
    this.$refs.sortFormRef.validate(valid => {
      if (valid) {
        // 创建更新对象，复用现有表单结构
          const updateData = {
            ...this.currentSortRow,   // 复制原有数据
            sortNumber: this.sortForm.sortNumber // 更新排序字段
          };
           // 调用修改接口
          updateOperation(updateData).then(response => {
            if (response.code === 200) {
              this.$message.success("排列顺序修改成功");
              // 更新本地数据
              this.currentSortRow.sortNumber = this.sortForm.sortNumber;
              // 关闭弹窗
              this.sortDialogVisible = false;
            }
          }).catch(() => {
            this.$message.error("修改失败");
          });
        }
      });
    },
    /** 查询工序列表 */
    getList() {
      this.loading = true;
      listOperation(this.queryParams).then(response => {
        this.operationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getList2() {
      this.loadingParameter = true;
      this.form2={};
      listParameter(this.Params).then(response => {
        this.parameterList = response.rows;
        this.total = response.total;
        this.loadingParameter = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.openSort = false;
      this.reset();
    },
    cancel2() {
      this.open2 = false;
      this.open2 = false;
      this.form2={};
      this.reset2();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        parameCheck: null,
        processValidityPeriod: null,
        sort: null,
        sortNumber: null,
        apsStr: null,
        apsEnable: null,
        siteId: null,
        operationType: null,
        code: null,
        name: null,
        description: null,
        cycleTime: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工序";
    },
    handleAdd2() {
      // this.reset2();
      this.open2 = true;
      this.title2 = "添加工序属性";
    },

    // 批量排序按钮
    handleBatchSort(){
    this.reset();
    this.openSort = true;
    this.isBatchFullscreen = false; // 重置全屏状态
    },
    // 切换批量排序弹窗全屏状态
    toggleBatchFullscreen() {
      this.isBatchFullscreen = !this.isBatchFullscreen;
      
      // 调整布局，避免全屏时内容错位
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 100);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getOperation(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工序";
      });
    },
    handleUpdate2(row) {
      // this.reset();
      const id = row.id || this.ids
      getParameter(id).then(response => {
        this.form2 = response.data;
        this.open2 = true;
        this.title2 = "修改工序属性";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateOperation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOperation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
     /** 排序提交按钮 */
    submitform() {
          this.openSort = false;
          this.getList();
    },
    /** 工序属性提交按钮 */ 
    submitForm2() {
      console.log("调用submitform2")
      this.$refs["form2"].validate(valid => {
        if (valid) {
          console.log(this.parameterList[0].workingProduceId);
          this.form2.workingProduceId = this.parameterList[0].workingProduceId;
          console.log(this.form2);
          if (this.form2.id != null) {
            updateParameter(this.form2).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open2 = false;
              this.getList2();
            });
          } else {
            addParameter(this.form2).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open2 = false;
              this.getList2();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除工序编号为"' + ids + '"的数据项？').then(function() {
        return delOperation(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    handleDelete2(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除工序属性编号为"' + ids + '"的数据项？').then(function() {
        return delParameter(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mes/operation/export', {
        ...this.queryParams
      }, `工序_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
<style scoped>
/* 全屏模式下的样式调整 */
.batch-sort-dialog .el-dialog__header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.batch-sort-dialog .el-dialog__body {
  padding: 15px 20px;
}

.dialog-toolbar {
  padding: 10px 0;
  margin-bottom: 10px;
}

.dialog-content {
  margin-bottom: 15px;
}

.dialog-footer {
  text-align: right;
  padding-top: 15px;
  border-top: 1px solid #eee;
}
</style>