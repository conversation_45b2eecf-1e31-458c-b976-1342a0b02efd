<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            class="dashed-container"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['quality:config:add']"
            >新增检验配置</el-button
          >
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="5">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item>
              <el-input
                v-model="queryParams.materialCode"
                placeholder="请输入物料名称或编码"
                clearable
                @input="handleInputSearch"
              />
            </el-form-item>
          </el-form>
          <el-tree
            :data="configList"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            ref="tree"
            :highlight-current="true"
            class="custom-tree"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ node.label }}</span>
              <span>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => append(data)"
                  v-hasPermi="['quality:config:edit']"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="remove(node, data)"
                  v-hasPermi="['quality:config:remove']"
                >
                  删除
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-col>
        <el-col :span="19">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAddInfo(materialId)"
                v-hasPermi="['quality:infoConf:add']"
                >新增项目</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="el-icon-edit"
                size="mini"
                :disabled="single"
                @click="handleUpdateInfo"
                v-hasPermi="['quality:infoConf:edit']"
                >修改</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plainthis.form
                icon="el-icon-delete"
                size="mini"
                :disabled="multiple"
                @click="handleDeleteInfo"
                v-hasPermi="['quality:infoConf:remove']"
                >删除</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="el-icon-download"
                size="mini"
                @click="handleExportInfo"
                v-hasPermi="['quality:infoConf:export']"
                >导出</el-button
              >
            </el-col>
            <right-toolbar
              :showSearch.sync="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>

          <el-table
            height="62vh"
            v-loading="loading"
            :data="infoList"
            @selection-change="handleSelectionChange"
            :row-class-name="tableRowClassName"
          >
            <el-table-column type="index" width="55" align="center" />
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="检验分组"
              align="center"
              prop="inspectionGroup"
            />
            <el-table-column
              label="检验方法"
              align="center"
              prop="inspectionMethod"
            />
            <el-table-column
              label="检验项目编码"
              align="center"
              prop="itemCode"
              width="120"
            />
            <el-table-column
              label="检验项目名称"
              align="center"
              prop="itemName"
              width="120"
            />
            <el-table-column
              label="检验类型编码"
              align="center"
              prop="classCode"
              width="120"
            />
            <el-table-column
              label="检验类型名称"
              align="center"
              prop="className"
              width="120"
            />
            <el-table-column
              label="标准值"
              align="center"
              prop="standardValue"
            />
            <el-table-column label="上限值" align="center" prop="upperLimit" />
            <el-table-column label="下限值" align="center" prop="lowerLimit" />

            <el-table-column
              label="技术标准"
              align="center"
              prop="technicalStandard"
            />
            <el-table-column
              label="抽样方案名称"
              align="center"
              prop="inspSchemeName"
              width="120px"
            />
            <!-- <el-table-column
              label="严格度"
              align="center"
              prop="iqcInspConfigStrictness"
            >
              <template slot-scope="scope">
                <dict-tag
                  :options="dict.type.iqc_insp_config_strictness"
                  :value="scope.row.iqcInspConfigStrictness"
                />
              </template>
            </el-table-column> -->

            <el-table-column
              label="检验顺序"
              align="center"
              prop="inspectionSerial"
            />
            <el-table-column
              label="组内顺序"
              align="center"
              prop="groupSerial"
            />
            <!-- <el-table-column
              label="检验项目id"
              align="center"
              prop="inspectionItemType"
            /> -->

            <!-- <el-table-column
              label="抽样方案id"
              align="center"
              prop="inspScheme"
            /> -->
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              width="120"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdateInfo(scope.row)"
                  v-hasPermi="['quality:infoConf:edit']"
                  >编辑</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteInfo(scope.row)"
                  v-hasPermi="['quality:infoConf:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- 添加或修改OQC检验配置对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="OQC检验配置信息" name="1">
              <el-form-item
                label="物料编码"
                prop="materialCode"
                style="width: 240px"
              >
                <el-select
                  v-model="form.materialCode"
                  placeholder="请选择物料编码"
                  clearable
                  style="width: 240px"
                  @change="handleMaterialChange"
                >
                  <el-option
                    v-for="material in materialList"
                    :key="material.id"
                    :label="material.materialCode"
                    :value="material.materialCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item
                label="物料名称"
                prop="materialName"
                style="width: 240px"
              >
                <el-input
                  v-model="form.materialName"
                  placeholder="请输入物料名称"
                  disabled
                />
              </el-form-item>
              <el-form-item
                label="状态"
                prop="configStatus"
                style="width: 240px"
              >
                <el-radio v-model="form.configStatus" label="0">正常</el-radio>
                <el-radio v-model="form.configStatus" label="1">停用</el-radio>
              </el-form-item>
              <el-form-item label="备注" prop="remark" style="width: 700px">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入内容"
                />
              </el-form-item>
              <div class="demo-drawer__footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
              </div>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>

      <el-drawer
        :title="title"
        :visible.sync="openIqc"
        :size="'70%'"
        append-to-body
      >
        <el-collapse v-model="activeNamesInfo">
          <el-form ref="form" :model="queryParamsClass">
            <el-collapse-item title="IQC检验配置明细单信息" name="1">
              <el-form-item
                label="所属类型"
                prop="inspectionClassType"
                style="width: 240px"
                clearable
              >
                <el-select
                  v-model="queryParamsClass.inspectionClassType"
                  placeholder="请选择所属类型"
                  style="width: 240px"
                  clearable
                >
                  <el-option
                    v-for="item in classList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button
                  type="primary"
                  icon="el-icon-search"
                  size="mini"
                  @click="handleQueryClass"
                  >搜索</el-button
                >
                <el-button
                  icon="el-icon-refresh"
                  size="mini"
                  @click="resetQueryClass"
                  >重置</el-button
                >
              </el-form-item>
            </el-collapse-item>
          </el-form>
          <el-form>
            <el-collapse-item title="项目与模板信息" name="2">
              <el-tabs v-model="activeTab" type="card" class="demo-tabs">
                <el-tab-pane label="项目" name="item">
                  <!-- <el-tab>
            <el-tab-pane label="项目" name="item">ncsakcnascnasn</el-tab-pane>
          </el-tab> -->

                  <el-table
                    height="50vh"
                    v-loading="itemloading"
                    :data="itemList"
                    @selection-change="handleSelectionChangeProject"
                    ref="projectTable"
                    row-key="inspectionItemType"
                  >
                    <el-table-column
                      type="selection"
                      width="55"
                      align="center"
                    />
                    <el-table-column
                      label="序号"
                      type="index"
                      align="center"
                      prop="id"
                    />
                    <el-table-column
                      label="项目编码"
                      align="center"
                      prop="itemCode"
                    />
                    <el-table-column
                      label="项目名称"
                      align="center"
                      prop="itemName"
                    />
                    <!-- <el-table-column
            label="所属类型"
            align="center"
            prop="inspectionClassType"
          >
          </el-table-column> -->
                    <el-table-column
                      label="所属类型"
                      align="center"
                      prop="classCode"
                    >
                    </el-table-column>
                    <el-table-column
                      label="分析方法"
                      align="center"
                      prop="analysisMethod"
                    >
                      <template slot-scope="scope">
                        <dict-tag
                          :options="dict.type.analysis_method"
                          :value="scope.row.analysisMethod"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      label="单位"
                      align="center"
                      prop="itemUnit"
                    />
                    <el-table-column
                      label="检验方法"
                      align="center"
                      prop="inspectionMethod"
                    />
                    <el-table-column
                      label="标准值"
                      align="center"
                      prop="standardValue"
                    />
                    <el-table-column
                      label="上限值"
                      align="center"
                      prop="upperLimit"
                    />
                    <el-table-column
                      label="下限值"
                      align="center"
                      prop="lowerLimit"
                    />
                    <el-table-column
                      label="技术标准"
                      align="center"
                      prop="technicalStandard"
                    />
                    <el-table-column
                      label="结果类型"
                      align="center"
                      prop="resultType"
                    />
                    <el-table-column
                      label="数据来源"
                      align="center"
                      prop="dataSource"
                    />
                    <el-table-column
                      label="检验组数"
                      align="center"
                      prop="inspectionGroup"
                    />
                  </el-table>
                </el-tab-pane>
                <el-tab-pane label="模板" name="template">
                  <el-table
                    height="30vh"
                    v-loading="templateLoading"
                    :data="templateList"
                    @selection-change="handleSelectionChangeTemplate"
                    ref="multipleTable"
                  >
                    <el-table-column
                      type="selection"
                      width="55"
                      align="center"
                    />
                    <el-table-column
                      label="序号"
                      type="index"
                      align="center"
                      prop="id"
                    />
                    <el-table-column
                      label="模版编码"
                      align="center"
                      prop="templateName"
                    />
                    <el-table-column
                      label="模版名称"
                      align="center"
                      prop="templateCode"
                    />
                  </el-table>
                </el-tab-pane>
              </el-tabs>
              <h3 class="preview-title">未添加项目预览</h3>
              <!-- 选中项展示区域 -->
              <el-table :data="submitList" height="30vh">
                <el-table-column type="index" label="序号" width="55" />
                <el-table-column
                  label="项目名称"
                  align="center"
                  prop="itemName"
                />
                <el-table-column
                  label="检验方法"
                  align="center"
                  prop="inspectionMethod"
                />
                <el-table-column
                  label="分析方法"
                  align="center"
                  prop="analysisMethod"
                >
                  <template slot-scope="scope">
                    <dict-tag
                      :options="dict.type.analysis_method"
                      :value="scope.row.analysisMethod"
                    />
                  </template>
                </el-table-column>
                <el-table-column
                  label="标准值"
                  align="center"
                  prop="standardValue"
                />
                <el-table-column
                  label="上限值"
                  align="center"
                  prop="upperLimit"
                />
                <el-table-column
                  label="下限值"
                  align="center"
                  prop="lowerLimit"
                />
              </el-table>
            </el-collapse-item>
          </el-form>
        </el-collapse>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitFormIqc">确 定</el-button>
          <el-button @click="openIqc = false">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 编辑弹窗 -->
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="30%"
        :before-close="handleClose"
      >
        <el-form
          ref="formInfo"
          :model="formInfo"
          :inline="true"
          label-width="100px"
          :rules="infoRules"
        >
          <el-form-item label="抽样方案" prop="inspScheme">
            <el-select
              v-model="formInfo.inspScheme"
              placeholder="请选择抽样方案"
              style="width: 260px"
              filterable
              clearable
              @change="inspSchemeChange"
            >
              <el-option
                v-for="(item, index) in schemeList"
                :key="index"
                :label="item.schemeName + '-' + item.schemeCode"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="检验分组" prop="inspectionGroup">
            <el-input
              v-model="formInfo.inspectionGroup"
              placeholder="请输入检验分组"
            />
          </el-form-item>
          <el-form-item label="检验顺序" prop="inspectionSerial">
            <el-input
              v-model="formInfo.inspectionSerial"
              placeholder="请输入检验顺序"
            />
          </el-form-item>
          <el-form-item label="标准值" prop="standardValue">
            <el-input
              v-model="formInfo.standardValue"
              placeholder="请输入标准值"
            />
          </el-form-item>
          <el-form-item label="上限值" prop="upperLimit">
            <el-input
              v-model="formInfo.upperLimit"
              placeholder="请输入上限值"
            />
          </el-form-item>
          <el-form-item label="下限值" prop="lowerLimit">
            <el-input
              v-model="formInfo.lowerLimit"
              placeholder="请输入下限值"
            />
          </el-form-item>
          <el-form-item label="单位" prop="unit">
            <el-input v-model="formInfo.unit" placeholder="请输入单位" />
          </el-form-item>
          <el-form-item label="检验方法" prop="inspectionMethod">
            <el-input
              v-model="formInfo.inspectionMethod"
              placeholder="请输入检验方法"
            />
          </el-form-item>
          <el-form-item label="技术标准" prop="technicalStandard">
            <el-input
              v-model="formInfo.technicalStandard"
              placeholder="请输入技术标准"
            />
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="submitFormInfo">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import {
//   listConfig,
//   getConfig,
//   delConfig,
//   addConfig,
//   updateConfig,
// } from "@/api/quality/base/config";

import {
  listOqcconfig,
  getOqcconfig,
  delOqcconfig,
  addOqcconfig,
  updateOqcconfig,
} from "@/api/quality/oqcconfig";

import { listItemByTemplate } from "@/api/quality/item";
import { listUnit } from "@/api/system/unit";
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
} from "@/api/quality/base/infoConf";

import {
  listOqcinfo,
  getOqcinfo,
  delOqcinfo,
  addOqcinfo,
  updateOqcinfo,
} from "@/api/quality/oqcinfo";

import { listMaterial } from "@/api/system/material";
import { listClass } from "@/api/quality/base/class";
import { getTemplateByClass, listTemplate } from "@/api/quality/base/template";
import { listScheme } from "@/api/quality/scheme";
import { debounce } from "lodash-es";

export default {
  name: "Config",
  dicts: [
    "compare_state",
    "iqc_insp_config_strictness",
    "inspection_method_enum",
    "analysis_method",
  ],
  data() {
    return {
      // 抽样方法的list
      schemeList: [],
      // 采购主界面点击新增的时候的dialog控制变量
      dialogVisible: false,

      templateLoading: false,
      selectedItems: [], // 已选中的数据项
      // 检验模版
      templateList: [],
      // 选中的项目list
      submitList: [],

      activeTab: "item",
      materialId: null,
      activeNames: ["1", "2"],
      activeNamesInfo: ["1", "2"],
      materialList: [], //获取物料列表
      // 遮罩层
      itemLoading: false,
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // OQC检验配置表格数据
      configList: [],
      // 检验配置明细表格数据
      infoList: [],
      //检验配置明细的ids
      inspectionItemTypes: [],
      //IQC检验配置项目编码数组
      itemCodes: [],
      // 检验项目表格数据
      classList: [],
      itemList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openIqc: false,
      openinfo: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        oqcInspConfigType: null,
        upperLimit: null,
        lowerLimit: null,
        standardValue: null,
        technicalStandard: null,
        iqcInspConfigStrictness: null,
        inspectionMethod: null,
        classCode: null,
        className: null,
        inspectionGroup: null,
        inspectionSerial: null,
        groupSerial: null,
        inspectionItemType: null,
        itemCode: null,
        itemName: null,
        inspScheme: null,
        inspSchemeName: null,
        inspectionMethodEnum: null,
        unit: null,
        userSeqIndex: null,
        sampleControl: null,
        upperType: null,
        lowerType: null,
        comId: null,
      },
      // 查询参数
      queryParamsClass: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        name: null,
        comId: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      },
      queryParamsload: {
        pageNum: 1,
        pageSize: 10000,
        // 检验模版
        // templateCode: null,
        // templateName: null,
      },
      selectTemplateList: [], //选中的模板list
      //选中的项目list
      selectProjectList: [],
      // 表单参数
      form: {},
      formInfo: {},
      // 表单校验
      rules: {
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" },
        ],
        configStatus: [
          { required: true, message: "状态不能为空", trigger: "blur" },
        ],
      },
      // 表单校验
      rulesInfo: {
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
        upperLimit: [],
        lowerLimit: [],
        standardValue: [],
      },
      infoRules: {
        inspScheme: [
          { required: true, message: "抽样方案不能为空", trigger: "blur" },
        ],
        inspectionGroup: [
          { required: true, message: "检验分组不能为空", trigger: "blur" },
        ],
        inspectionSerial: [
          { required: true, message: "检验顺序不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    this.loadMaterialList();
    this.getListClass();
    this.getlistItem();
    this.getlistTemplate();
    this.loadSchemeList();
    this.rulesInfo.standardValue.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.rulesInfo.upperLimit.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
    this.rulesInfo.lowerLimit.push({
      validator: this.validateNumber,
      trigger: "blur",
    });
  },
  methods: {
    // 带防抖的输入处理
    handleInputSearch: debounce(function () {
      // 统一在此处处理查询，移除watch中的重复逻辑
      this.handleQuery();
    }, 300),
    inspSchemeChange(id) {
      console.log(id, "schemeCode");
      // 根据物料编码查找对应的物料信息
      let obj = this.schemeList.find((item) => item.id === id);
      this.formInfo.inspSchemeName = obj.schemeName;
    },
    //判断字段属性并修改背景颜色
    tableRowClassName({ row, rowIndex }) {
      if (!row.inspScheme) {
        return "success-row";
      } else {
        return "";
      }
    },
    // 重置查询条件并重新加载所属类型数据
    resetQueryClass() {
      this.queryParamsClass = {
        pageNum: 1,
        pageSize: 10,
        code: null,
        name: null,
        comId: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      };
      this.getListClass(); // 重新加载所属类型数据
    },
    handleClose() {
      this.dialogVisible = false;
    },
    /** 查询检验项目列表 */
    getlistItem() {
      this.itemloading = true;
      getTemplateByClass(this.queryParamsClass).then((response) => {
        console.log(this.queryParamsClass);
        this.itemList = response.data;
        if (this.itemList.length > 0 && this.infoList.length > 0) {
          this.itemList = this.itemList.filter(
            (item) => !this.inspectionItemTypes.has(item.id)
          );
        }
        // this.total = response.total;
        this.itemloading = false;
      });
    },

    /** 查询检验项目列表 */
    getlistTemplate() {
      this.templateLoading = true;
      listTemplate(this.queryParamsload).then((response) => {
        console.log(this.queryParamsload);
        this.templateList = response.rows;
        console.log("this.templateList------------->", this.templateList);
        // this.total = response.total;
        this.templateLoading = false;
      });
    } /** 查询所属类型 */,
    getListClass() {
      this.loading = true;
      listClass(this.queryParams).then((response) => {
        console.log(this.queryParams);
        this.classList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索项目按钮操作 */
    handleQueryClass(row) {
      this.queryParamsClass.pageNum = 1;
      this.getlistItem();
    },
    /** 查询所属类型 */
    getListClass() {
      this.itemloading = true;
      listClass(this.queryParamsClass).then((response) => {
        console.log(this.queryParamsClass);
        this.classList = response.rows;
        this.total = response.total;
        this.itemloading = false;
      });
    },
    // 自定义校验规则：检查是否为数字
    validateNumber(rule, value, callback) {
      if (value === null || value === undefined || value === "") {
        callback(); // 允许为空
      } else if (!/^\d+(\.\d+)?$/.test(value)) {
        callback(new Error("请输入有效数字")); // 非数字时提示错误
      } else {
        callback(); // 校验通过
      }
    },
    // 监听物料编码选择变化
    handleMaterialChange(materialCode) {
      // 根据物料编码查找对应的物料信息
      const selectedMaterial = this.materialList.find(
        (material) => material.materialCode === materialCode
      );

      if (selectedMaterial) {
        // 更新物料 ID 和物料名称
        this.form.materialId = selectedMaterial.id;
        this.form.materialName = selectedMaterial.materialName;
      } else {
        // 如果未找到对应的物料，清空相关字段
        this.form.materialId = null;
        this.form.materialName = null;
      }
    },
    /** 查询IQC检验配置列表 */
    getList() {
      this.loading = true;
      listOqcconfig(this.queryParams).then((response) => {
        // 对返回的数据进行处理，拼接 label 字段
        this.configList = response.rows.map((item) =>
          this.formatTreeNode(item)
        );
        this.total = response.total;
        // 确保树形数据已加载
        this.$nextTick(() => {
          if (this.formInfo.oqcInspConfigType) {
            this.$refs.tree.setCurrentKey(this.formInfo.oqcInspConfigType);
          } else {
            this.queryParams.oqcInspConfigType = this.configList[0].id;
            this.$refs.tree.setCurrentKey(this.configList[0].id); // 默认选中树形控件的第一个节点
            this.formInfo.oqcInspConfigType = this.configList[0].id;
          }
          this.getInfoList();
        });
        this.loading = false;
      });
    },
    // 格式化树节点数据
    formatTreeNode(node) {
      console.log("当前节点数据:", node); // 调试日志
      return {
        ...node,
        label: `${node.materialName}[${node.materialCode}]`, // 拼接物料名称和物料编码
        children: node.children
          ? node.children.map((child) => this.formatTreeNode(child))
          : [],
      };
    },
    // 获取检验配置明细列表
    getInfoList() {
      this.loading = true;
      listOqcinfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.inspectionItemTypes = new Set(
          this.infoList.map((item) => item.inspectionItemType)
        );
        this.total = response.total;
        this.loading = false;
      });
    },
    // 加载物料列表
    loadMaterialList() {
      listMaterial().then((response) => {
        this.materialList = response.rows.map((item) => ({
          id: item.id,
          materialCode: item.materialCode,
          materialName: item.materialName,
        }));
      });
    },
    // 加载抽样方法
    loadSchemeList() {
      listScheme(this.queryParamsload).then((response) => {
        this.schemeList = response.rows;
        console.log("this.schemeList------------->", this.schemeList);
      });
    },
    // 物料树节点点击事件
    handleNodeClick(node) {
      this.queryParams.oqcInspConfigType = node.id; // 将节点的 id 作为检验配置主表ID
      this.formInfo.oqcInspConfigType = node.id; // 同步赋值节点的 id
      this.getInfoList();
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelInfo() {
      this.openinfo = false;
      this.resetInfo();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        configStatus: "0",
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("form");
    },
    // 表单重置
    resetInfo() {
      this.formInfo = {
        id: null,
        oqcInspConfigType: null,
        upperLimit: null,
        lowerLimit: null,
        standardValue: null,
        technicalStandard: null,
        iqcInspConfigStrictness: null,
        inspectionMethod: null,
        classCode: null,
        className: null,
        inspectionGroup: null,
        inspectionSerial: null,
        groupSerial: null,
        inspectionItemType: null,
        itemCode: null,
        itemName: null,
        inspScheme: null,
        inspSchemeName: null,
        inspectionMethodEnum: null,
        unit: null,
        userSeqIndex: null,
        sampleControl: null,
        upperType: null,
        lowerType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.resetForm("formInfo");
    },
    resetFormClass() {
      this.form = {
        pageNum: 1,
        pageSize: 10,
        code: null,
        name: null,
        comId: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQueryInfo() {
      this.queryParams.pageNum = 1;
      this.getInfoList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQueryInfo();
    },
    /** 重置按钮操作 */
    resetQueryClass() {
      this.resetFormClass("queryFormClass");
      this.handleQueryClass();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // 获取选中项的ID数组
      this.ids = selection.map((item) => item.id);
      this.itemCodes = selection.map((item) => item.itemCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    getListTemplateInfo(ids) {
      listItemByTemplate(ids).then((response) => {
        if (ids.length == 0) {
          this.selectTemplateList = [];
        }
        if (response.rows.length > 0 && this.infoList.length > 0) {
          this.selectTemplateList = response.rows.filter(
            (item) => !this.inspectionItemTypes.has(item.inspectionItemType)
          );
          console.log(
            response.rows.filter((item) =>
              this.inspectionItemTypes.has(item.inspectionItemType)
            ),
            "=selectTemplateList"
          );
        } else {
          this.selectTemplateList = response.rows;
        }
        console.log(this.selectTemplateList, "=selectTemplateList");

        console.log("itemListbyIds:------------------>", response.rows);
        // this.submitList = this.itemListbyIds;
        console.log("submitList:------------------>", this.submitList);
        let newArr = [];
        const deSelectIdSet = new Set(
          this.selectProjectList.map((item) => item.id)
        );
        let projectArr = this.submitList.filter((item) =>
          deSelectIdSet.has(item.inspectionItemType)
        );
        if (ids.length == 0) {
          this.submitList = projectArr;
          return;
        }
        if (this.selectTemplateList.length == 0) {
          this.submitList = projectArr;
        } else {
          for (let i = 0; i < this.selectTemplateList.length; i++) {
            const item = this.selectTemplateList[i];
            item.oqcInspConfigType = this.queryParams.oqcInspConfigType;
            item.inspectionItemType = item.inspectionItemType;
            let obj = { ...item };
            let objs = this.submitList.find(
              (it) => it.inspectionItemType === item.inspectionItemType
            );
            console.log(objs, "objs");
            if (objs != null) {
              obj.id = objs.id;
              const index = this.submitList.findIndex(
                (it) => it.inspectionItemType === item.inspectionItemType
              );
              this.submitList[index] = obj;
            } else {
              this.$delete(obj, "id");
              newArr.push({ ...obj });
            }
            // this.submitList.push(obj)
          }
          console.log(newArr, "---");
          this.submitList = [...this.submitList, ...newArr];

          const deSelectIdSets = new Set(
            this.selectTemplateList.map((item) => item.inspectionItemType)
          );
          //过滤出不包含项目的数据
          let templateArr = this.submitList.filter(
            (item) => !deSelectIdSet.has(item.inspectionItemType)
          );
          //然后再过滤出本次勾选的模板项目
          let templateArr2 = templateArr.filter((item) =>
            deSelectIdSets.has(item.inspectionItemType)
          );
          console.log(projectArr, templateArr2, "---this.submitList");
          //最后值=模板下的项目+本次勾选的项目
          this.submitList = [...projectArr, ...templateArr2];
        }
      });
      // this.itemList
      // this.submitList
    },
    //项目多选
    handleSelectionChangeProject(selection) {
      this.selectProjectList = selection;
      let newArr = [];
      console.log(this.selectTemplateList, "this.selectTemplateList");
      const deSelectIdSet = new Set(
        this.selectTemplateList.map((item) => item.inspectionItemType)
      );
      let templateArr = this.submitList.filter((item) =>
        deSelectIdSet.has(item.inspectionItemType)
      );
      if (this.selectProjectList.length == 0) {
        this.submitList = templateArr;
      } else {
        for (let i = 0; i < this.selectProjectList.length; i++) {
          const item = this.selectProjectList[i];
          console.log(item, "item---");
          item.oqcInspConfigType = this.queryParams.oqcInspConfigType;
          item.inspectionItemType = item.id;
          let obj = { ...item };
          let objs = this.submitList.find(
            (it) => it.inspectionItemType === item.id
          );
          console.log(objs, "objs");
          if (objs != null) {
            obj.id = objs.id;
            console.log(item.id, this.submitList, "item.id");
            const index = this.submitList.findIndex(
              (it) => it.inspectionItemType === item.id
            );
            this.submitList[index] = obj;
          } else {
            this.$delete(obj, "id");
            newArr.push({ ...obj });
          }
        }

        this.submitList = [...this.submitList, ...newArr];
        const deSelectIdSets = new Set(
          this.selectProjectList.map((item) => item.id)
        );
        //过滤出不包含模板的数据
        let projectArr = this.submitList.filter(
          (item) => !deSelectIdSet.has(item.inspectionItemType)
        );
        //然后再过滤出本次勾选的项目
        let projectArr2 = projectArr.filter((item) =>
          deSelectIdSets.has(item.inspectionItemType)
        );
        //最后值=模板下的项目+本次勾选的项目
        this.submitList = [...templateArr, ...projectArr2];
      }

      // this.submitList = selection;
    },
    //模板多选
    handleSelectionChangeTemplate(selection) {
      this.selectedItems = selection; // 直接保存完整数据对象
      // this.ids = selection.map((item) => item.id);
      const ids = selection.map((item) => item.id);

      console.log("this.ids:------------------>", ids);
      this.getListTemplateInfo(ids);
      // 更新选择状态
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // 判断某个项是否被选中
    isSelected(item) {
      return this.selectedItems.some((selected) => selected.id === item.id);
    },
    // 切换选择状态（用于展示区域的按钮）
    // toggleSelection(item) {
    //   const index = this.selectedItems.findIndex(
    //     (selected) => selected.id === item.id
    //   );
    //   if (index > -1) {
    //     this.selectedItems.splice(index, 1);
    //   } else {
    //     this.selectedItems.push(item);
    //   }
    //   this.$refs.multipleTable.toggleRowSelection(item);
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加OQC检验配置";
    },
    /** 修改按钮操作 */
    append(row) {
      this.reset();
      const id = row.id || this.ids;
      getOqcconfig(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改OQC检验配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateOqcconfig(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOqcconfig(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    // remove(node, data) {
    //   const parent = node.parent;
    //   const children = parent.data.children || parent.data;
    //   const index = children.findIndex((d) => d.id === data.id);
    //   children.splice(index, 1);
    // },
    /** 删除按钮操作 */
    remove(node, data) {
      // const parent = node.parent;
      // const children = parent.data.children || parent.data;
      // const index = children.findIndex((d) => d.id === data.id);
      // children.splice(index, 1);
      // const ids = row.id || this.ids;
      const ids = data.id || this.ids;
      const code = data.materialCode || this.ids;
      this.$modal
        .confirm('是否确认删除编号"' + code + '"为数据项？')
        .then(function () {
          return delOqcconfig(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/config/export",
        {
          ...this.queryParams,
        },
        `IQC检验配置_${new Date().toLocaleDateString()}.xlsx`
      );
    },

    /// IQC检验配置明细操作
    /** 新增按钮操作 */
    handleAddInfo() {
      this.submitList = [];
      this.selectTemplateList = [];
      this.selectProjectList = [];
      this.openIqc = true;
      //项目list查询
      this.getlistItem();
      //模板list查询
      this.getlistTemplate();
      this.resetInfo();
      this.formInfo.oqcInspConfigType = this.queryParams.oqcInspConfigType; // 自动赋值检验配置 ID

      this.title = "添加IQC检验配置明细";
    },
    /** 修改按钮操作 */
    handleUpdateInfo(row) {
      console.log("row:------------------>", row);
      this.dialogVisible = true;
      // this.reset();
      const id = row.id || this.ids;
      this.title = "编辑IQC检验配置明细";
      getOqcinfo(id).then((response) => {
        this.formInfo = response.data;
        console.log("this.formInfo:------------------>", this.formInfo);
        this.openinfo = true;
      });
      // this.title = "编辑IQC检验配置明细";
    },
    //新增检验明细
    submitFormIqc() {
      if (this.submitList.length == 0) {
        this.$message({
          message: "请至少添加一条项目",
          type: "warning",
        });
        return;
      }
      this.formInfo.oqcInspConfigInfoList = this.submitList;
      addOqcinfo(this.formInfo).then((response) => {
        this.$modal.msgSuccess("新增成功");
        this.openIqc = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitFormInfo() {
      console.log("this.formInfo:------------------>", this.formInfo);
      this.$refs["formInfo"].validate((valid) => {
        console.log("valid:------------------>", valid);
        if (valid) {
          if (this.formInfo.id != null) {
            updateOqcinfo(this.formInfo).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.dialogVisible = false;
              this.getInfoList();
            });
          } else {
            addOqcinfo(this.formInfo).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.dialogVisible = false;
              this.getInfoList();
            });
            console.log("新增成功");
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDeleteInfo(row) {
      const ids = row.id || this.ids;
      const code = row.itemCode || this.itemCodes;
      this.$modal
        .confirm('是否确认删除"' + code + '"该数据项？')
        .then(function () {
          return delOqcinfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExportInfo() {
      this.download(
        "quality/info/export",
        {
          ...this.queryParams,
        },
        `IQC检验配置明细_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss">
h3 {
  display: block;
  font-size: 1.17em;
  margin-block-start: 1em;
  margin-block-end: 1em;
  margin-inline-start: 0px;
  margin-inline-end: 0px;
  font-weight: bold;
  unicode-bidi: isolate;
}
.el-table .success-row {
  background: #f1cbc4 !important;
  color: #000;
}

// 表单外围虚线框
.dashed-container {
  border: 1px dashed #ccc;
  padding: 8px 24px;            /* 更大的内边距，适配表单内容 */
  border-radius: 8px;       /* 更大的圆角 */
  background: #fff;         /* 白色背景，与虚线对比更明显 */
  color: black;
  background-color: #fff;
  display: flex;
  justify-content: center;    /* 水平居中 */
  margin-left: 50px;
  margin-top: 10px;
}
</style>
