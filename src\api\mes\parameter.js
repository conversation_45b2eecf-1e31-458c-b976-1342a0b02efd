import request from '@/utils/request'

// 查询工序属性列表
export function listParameter(query) {
  return request({
    url: '/mes/parameter/list',
    method: 'get',
    params: query
  })
}

// 查询工序属性详细
export function getParameter(id) {
  return request({
    url: '/mes/parameter/' + id,
    method: 'get'
  })
}

// 新增工序属性
export function addParameter(data) {
  return request({
    url: '/mes/parameter',
    method: 'post',
    data: data
  })
}

// 修改工序属性
export function updateParameter(data) {
  return request({
    url: '/mes/parameter',
    method: 'put',
    data: data
  })
}

// 删除工序属性
export function delParameter(id) {
  return request({
    url: '/mes/parameter/' + id,
    method: 'delete'
  })
}
