import request from '@/utils/request'

// 查询工单BOM列表
export function listTrcWorkOrderItems(query) {
  return request({
    url: '/mes/trcWorkOrderItems/list',
    method: 'get',
    params: query
  })
}

// 查询工单BOM详细
export function getTrcWorkOrderItems(id) {
  return request({
    url: '/mes/trcWorkOrderItems/' + id,
    method: 'get'
  })
}

// 新增工单BOM
export function addTrcWorkOrderItems(data) {
  return request({
    url: '/mes/trcWorkOrderItems',
    method: 'post',
    data: data
  })
}

// 修改工单BOM
export function updateTrcWorkOrderItems(data) {
  return request({
    url: '/mes/trcWorkOrderItems',
    method: 'put',
    data: data
  })
}

// 删除工单BOM
export function delTrcWorkOrderItems(id) {
  return request({
    url: '/mes/trcWorkOrderItems/' + id,
    method: 'delete'
  })
}
