<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-row :gutter="10" class="mb8">
        <el-col :span="8">
          <el-button
          class="dashed-container"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['quality:template:add']"
            >新增模版</el-button
          >
        </el-col>

        <!-- <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar> -->
      </el-row>

      <el-row :gutter="10" class="mb8">
        <el-col :span="5">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
               <el-form-item>
              <el-input
                v-model="queryParams.templateCode"
                placeholder="请输入检验模版名称或编码"
                clearable
                @input="handleInputSearch"
              />
            </el-form-item>          
          </el-form>
          
          <el-tree
            :data="templateList"
            node-key="id"
            default-expand-all
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            ref="tree"
            :highlight-current="true"
            class="custom-tree"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>{{ data.templateCode }}-{{ data.templateName }}</span>
                  
              <span>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => append(data)"
                  v-hasPermi="['quality:template:edit']"
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="() => remove(node, data)"
                  v-hasPermi="['quality:template:remove']"
                >
                  删除
                </el-button>
              </span>
            </span>
          </el-tree>
        </el-col>
        <el-col :span="19">
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="el-icon-plus"
                size="mini"
                @click="handleAddInfo"
                v-hasPermi="['quality:info:add']"
                >加项目到模版</el-button
              >
            </el-col>
          </el-row>
          <el-table
            height="62vh"
            v-loading="loading"
            :data="infoList"
            @selection-change="handleSelectionChange"
          >
          <el-table-column type="index" width="55" align="center" />
            <el-table-column type="selection" width="55" align="center" />

            <el-table-column
              label="检验项目编号"
              align="center"
              prop="itemCode"
            />
            <el-table-column
              label="检验项目名称"
              align="center"
              prop="itemName"
            />
            <el-table-column label="检验类型编码" align="center" prop="code" />
            <el-table-column label="检验类型名称" align="center" prop="name" />
            <el-table-column
              label="标准值"
              align="center"
              prop="standardValue"
            />
            <el-table-column label="上限值" align="center" prop="upperLimit" />
            <el-table-column label="下限值" align="center" prop="lowerLimit" />
            <el-table-column
              label="技术标准"
              align="center"
              prop="technicalStandard"
            />
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['quality:info:remove']"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- 添加或修改检验模板对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="检验模板信息" name="1">
          <el-form-item label="编码" prop="templateCode" style="width: 240px">
            <el-input v-model="form.templateCode" placeholder="请输入编码" />
          </el-form-item>
          <el-form-item label="名称" prop="templateName" style="width: 240px">
            <el-input v-model="form.templateName" placeholder="请输入名称" />
          </el-form-item>
          <el-form-item label="备注" prop="remark" style="width: 700px">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
            />
          </el-form-item>
          <!-- <el-form-item label="组织" prop="comId" style="width: 240px">
            <el-input v-model="form.comId" placeholder="请输入组织" />
          </el-form-item> -->
          <!-- <el-form-item label="删除标志" prop="delFlag" style="width: 240px">
            <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
          </el-form-item> -->
          </el-collapse-item>
          </el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>

      <!-- 添加或修改检验模板明细对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="openTemplate"
        :size="'75%'"
        append-to-body
      >        
      <el-collapse v-model="activeNamesInfo">
        <!-- 搜索框 -->
        <el-form
          ref="form"
          :model="queryParams"
          style="height: 150px"
        >
            <el-collapse-item title="生产发料单信息" name="1">
          <el-row :gutter="10" class="mb8">
            <el-col :span="6">
              <el-form-item
                label="项目编码"
                prop="itemName"
                style="width: 240px"
              >
                <!-- <el-input
                  v-model="formItem.itemCode"
                  placeholder="请输入项目编码"
                /> -->
                <el-input
                  v-model="queryParams.itemCode"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="所属类型"
                prop="inspectionClassType"
                style="width: 240px"
                clearable
              >
                <el-select
                  v-model="queryParams.inspectionClassType"
                  placeholder="请选择所属类型"
                  style="width: 240px"
                  clearable
                >
                  <el-option
                    v-for="item in classList"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6"
              ><el-form-item
                label="项目名称"
                prop="itemName"
                style="width: 240px; height: 40px"
              >
                <el-input
                  v-model="queryParams.itemName"
                  placeholder="请输入项目名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                /> </el-form-item
            ></el-col>
          </el-row>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQueryItem"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
              >重置</el-button
            >
          </el-form-item>
          </el-collapse-item>
        </el-form>
        <!-- iqc检验项目表格 -->

        <el-form>
          <el-collapse-item title="iqc检验项目信息" name="2">
          <el-form-item>
            <el-button
              style="width: 150px"
              type="primary"
              @click="pushListTemplateInfo"
              >向下追加</el-button
            >
          </el-form-item>
          <el-table
            height="50vh"
            v-loading="loading"
            :data="itemList"
            @selection-change="handleSelectionChange"
            ref="multipleTable"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="项目编码" align="center" prop="itemCode" />
            <el-table-column label="项目名称" align="center" prop="itemName" />
            <!-- <el-table-column
            label="所属类型"
            align="center"
            prop="inspectionClassType"
          >
          </el-table-column> -->
            <el-table-column label="所属类型" align="center" prop="code">
            </el-table-column>
            <el-table-column
              label="分析方法"
              align="center"
              prop="analysisMethod"
            >
            </el-table-column>
            <el-table-column label="单位" align="center" prop="itemUnit" />
            <el-table-column
              label="检验方法"
              align="center"
              prop="inspectionMethod"
            />
            <el-table-column
              label="标准值"
              align="center"
              prop="standardValue"
            />
            <el-table-column label="上限值" align="center" prop="upperLimit" />
            <el-table-column label="下限值" align="center" prop="lowerLimit" />
            <el-table-column
              label="技术标准"
              align="center"
              prop="technicalStandard"
            />
            <el-table-column
              label="结果类型"
              align="center"
              prop="resultType"
            />
            <el-table-column
              label="数据来源"
              align="center"
              prop="dataSource"
            />
            <el-table-column
              label="检验组数"
              align="center"
              prop="inspectionGroup"
            />
          </el-table>
          </el-collapse-item>
        </el-form>
        <!--  -->
        <el-form>
          <el-collapse-item title="选中表单信息" name="3">

          <el-table height="50vh" v-loading="loading" :data="templateInfoList">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="检验类型编码" align="center" prop="code" />
            <el-table-column label="检验类型名称" align="center" prop="name" />
            <el-table-column
              label="检验项目名称"
              align="center"
              prop="itemName"
            />
            <el-table-column
              label="检验项目编码"
              align="center"
              prop="itemCode"
            />
            <el-table-column
              label="操作"
              align="center"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <!-- <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdateInfo(scope.row)"
                  v-hasPermi="['quality:info:add']"
                  >修改</el-button
                > -->
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdateInfo(scope.row)"
                  >修改</el-button
                >
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDeleteInfo(scope.row, scope.$index)"
                  v-hasPermi="['quality:info:remove']"
                  >删除</el-button
                >
                <el-dialog
                  :modal="false"
                  title="修改检验模版明细"
                  width="600px"
                  height="300px"
                  :visible.sync="dialogFormVisible"
                >
                  <el-form :model="currentEditRow">
                    <el-form-item label="检验顺序" prop="inspectionSerial">
                      <el-input
                        v-model="currentEditRow.inspectionSerial"
                        autocomplete="off"
                      />
                    </el-form-item>
                    <el-form-item label="检验分组" prop="inspectionGroup">
                      <el-input
                        v-model="currentEditRow.inspectionGroup"
                        autocomplete="off"
                      />
                    </el-form-item>
                  </el-form>
                  <span slot="footer" class="dialog-footer">
                    <el-button @click="handleClose">取 消</el-button>
                    <el-button
                      type="primary"
                      @click="addTemplateInfo(scope.$index)"
                    >
                      确认
                    </el-button>
                  </span>
                </el-dialog>
              </template>
            </el-table-column>
          </el-table>
          </el-collapse-item>
        </el-form>
        </el-collapse>

        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submittamplate">确 定</el-button>
          <el-button @click="cancelTemplate">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listTemplate,
  getTemplate,
  delTemplate,
  addTemplate,
  updateTemplate,
  getTemplateByClass,
} from "@/api/quality/base/template";

import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
} from "@/api/quality/info";
import { listClass } from "@/api/quality/base/class";
// 引入防抖函数
import { debounce } from 'lodash-es';
export default {
  name: "Template",
  data() {
    return {
      currentEditRow: {}, // 用于存储当前要修改的行数据
      //  data: JSON.parse(JSON.stringify(data)),
      // 遮罩层
      loading: false,
      itemloading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      activeNames: ["1", "2"],
activeNamesInfo: ["1", "2", "3"],
      // 总条数
      total: 0,
      // 检验模板表格数据
      templateList: [],
      // 检验项目表格数据
      itemList: [],
      // 检验项目表格数据
      classList: [],
      //检验模板明细
      infoList: [],
      // 检验模版详情表格数据
      templateInfoList: [],
      //多选框选中的list
      selectedList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      openTemplate: false,
      // 修改项目模版弹出层标
      dialogFormVisible: false,
      // 查询参数
      queryParams: {
        selectedClassId: null,
        // 所属类型下拉框数据
        pageNum: 1,
        pageSize: 10000,
        templateCode: null,
        templateName: null,
        comId: null,
        templateStatus: null,
        inspectionMethod: null,
        inspectionClassType: null,
        inspectionItemType: null,
        itemCode: null,
        itemName: null,
        code: null,
        name: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        groupSerial: null,
        inspectionSerial: null,
        inspectionGroup: null,
        inspectionTemplateType: null,
        comId: null,
      },
      // 查询参数
      queryParamsClass: {
        pageNum: 1,
        pageSize: 10,
        code: null,
        name: null,
        comId: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      },
      queryParamsInfo: {},
      // 表单参数
      form: {},
      formInfo: {},
      // 表单校验
      rules: {
        templateCode: [{ required: true, message: "编码不能为空", trigger: "blur" }],
        templateName: [{ required: true, message: "名称不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    console.log("awihdikahd");
    this.getList();
    // this.getlistItem();
    // this.getListClass();
  },
  methods: {
    // 带防抖的输入处理
   handleInputSearch: debounce(function() {
    // 统一在此处处理查询，移除watch中的重复逻辑
    this.handleQuery();
  }, 300),
    getList() {
      this.loading = true;
      listTemplate(this.queryParams).then((response) => {
        this.templateList = response.rows;
        // this.templateList = this.sortArrayByField(response.rows, 'createTime');
        // 确保树形数据已加载
        this.$nextTick(() => {
          if (this.formInfo.inspectionTemplateType) {
            this.$refs.tree.setCurrentKey(this.formInfo.inspectionTemplateType);
          } else {
            this.queryParamsInfo.inspectionTemplateType =
              this.templateList[0].id;
            this.$refs.tree.setCurrentKey(this.templateList[0].id); // 默认选中树形控件的第一个节点
            this.formInfo.inspectionTemplateType = this.templateList[0].id;
          }
          this.getInfo();
        });
        this.loading = false;
      });
    },
    getInfo() {
      listInfo(this.queryParamsInfo).then((res) => {
        this.infoList = res.rows;
        this.total = res.total;
      });
    },
    /** 查询检验项目列表 */
    getlistItem() {
      this.itemloading = true;
      getTemplateByClass(this.queryParams).then((response) => {
        // this.itemList = this.sortArrayByField(response.rows, 'createTime');
        this.itemList = response.rows;
        console.log(this.queryParamsClass);
        this.itemList = response.data;
        // this.total = response.total;
        this.itemloading = false;
      });
    },
    /** 查询所属类型 */
    getListClass() {
      this.loading = true;
      listClass(this.queryParams).then((response) => {
        console.log(this.queryParams);
        this.classList = response.rows;
        console.log("this.classLit----------------->", this.classList);
        this.total = response.total;
        this.loading = false;
      });
    },
    remove(node, data) {
      const ids = data.id || this.ids;
      const codes = data.templateCode || this.codes;
      this.$modal
        .confirm('是否确认删除检验模板编码为"' + codes + '"的数据项？')
        .then(function () {
          return delTemplate(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
      // const parent = node.parent;
      // const children = parent.data.children || parent.data;
      // const index = children.findIndex((d) => d.id === data.id);
      // children.splice(index, 1);
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    cancelTemplate() {
      this.openTemplate = false;
      this.reset();
    },
    // 点击新增以后 又取消了的时候的方法
    handleClose(done) {
      this.dialogFormVisible = false;
      // this.rest_importForm();
    },
    // 表单重置
    reset() {
      this.form = {
        // id: null,
        // templateCode: null,
        // templateName: null,
        // remark: null,
        // comId: null,
        // delFlag: null,
        // createBy: null,
        // createTime: null,
        // updateBy: null,
        // updateTime: null,
        // templateStatus: null,
        // itemCode: null,
        id: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        acceptanceLevel: null,
        destructiveTestNum: null,
        itemName: null,
        attachment: null,
        lowerType: null,
        upperType: null,
        inspectionEq: null,
        inspectionMethod: null,
        itemUnit: null,
        siteId: null,
        itemCode: null,
        inspectionClassType: null,
        inspectionLevel: null,
        inspectionSerial: null,
        analysisMethod: null,
        standardValue: null,
        upperLimit: null,
        lowerLimit: null,
        technicalStandard: null,
        resultType: null,
        dataSource: null,
        inspectionGroup: null,
        errorRangeLowerLimit: null,
        errorRangeUpperLimit: null,
      };
      this.resetForm("form");
    },

    // 处理树节点点击事件
    handleNodeClick(node) {
      this.queryParamsInfo.inspectionTemplateType = node.id; // 将节点的 id 作为所属类型
      this.formInfo.inspectionTemplateType = node.id; // 同步赋值节点的 id
      this.getInfo();
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 搜索项目按钮操作 */
    handleQueryItem(row) {
      this.queryParams.pageNum = 1;
      this.getlistItem();
    },
    /** 搜索项目按钮操作 */
    pushListTemplateInfo(row) {
      if (this.selectedList.length == 0) {
        this.$message({
          message: "请至少选择一条项目",
          type: "warning",
        });
        return;
      }
      const ids = row.id;
      console.log(ids);
      let newArr = [];
      for (let i = 0; i < this.selectedList.length; i++) {
        const item = this.selectedList[i];
        let obj = {};

        obj.inspectionTemplateType = this.formInfo.inspectionTemplateType;
        obj.code = item.code;
        obj.name = item.name;
        obj.inspectionClassType = item.classId;
        obj.inspectionItemType = item.id;
        obj.itemCode = item.itemCode;
        obj.itemName = item.itemName;
        obj.standardValue = item.standardValue;
        obj.upperLimit = item.upperLimit;
        obj.lowerLimit = item.lowerLimit;
        obj.technicalStandard = item.technicalStandard;
        console.log(item.id, "id");
        let objs = this.templateInfoList.find(
          (it) => it.inspectionItemType === item.id
        );
        console.log(objs, "objs");
        if (objs != null) {
          obj.id = objs.id;
          const index = this.templateInfoList.findIndex(
            (it) => it.inspectionItemType === item.id
          );
          this.templateInfoList[index] = obj;
        } else {
          newArr.push(obj);
        }
      }
      this.templateInfoList = [...this.templateInfoList, ...newArr];
      console.log(this.templateInfoList, newArr, "追加list");
      this.$refs.multipleTable.clearSelection();
      this.selectedList = [];
    },
    //删除模板明细
    handleDeleteInfo(row, index) {
      console.log(row, "row");
      if (!row.id) {
        this.templateInfoList.splice(index, 1);
      } else {
        delInfo(row.id).then((response) => {
          this.templateInfoList.splice(index, 1);
          this.$modal.msgSuccess("删除成功");
        });
      }
    },
    //修改模板
    handleUpdateInfo(row) {
      console.log(row, "row---------");
      // 复制当前行数据到临时对象
      this.currentEditRow = row;
      this.dialogFormVisible = true;
    },
    // 给明细添加分组和顺序
    addTemplateInfo(index) {
      // console.log(this.currentEditRow, "this.currentEditRow");
      // for (let item in this.templateInfoList) {
      //   if (item.inspectionItemType == this.currentEditRow.inspectionItemType) {
      //     item.inspectionSerial = this.currentEditRow.inspectionSerial;
      //     item.inspectionGroup = this.currentEditRow.inspectionGroup;
      //   }
      // }
      // this.dialogFormVisible = false;
      console.log(this.currentEditRow, "this.currentEditRow");
      // 使用 map 方法遍历数组
      this.templateInfoList = this.templateInfoList.map((item) => {
        if (
          item.inspectionItemType === this.currentEditRow.inspectionItemType
        ) {
          // 如果匹配到需要更新的元素，返回更新后的元素
          return {
            ...item,
            inspectionSerial: this.currentEditRow.inspectionSerial,
            inspectionGroup: this.currentEditRow.inspectionGroup,
          };
        }
        // 如果不匹配，返回原元素
        return item;
      });

      this.dialogFormVisible = false;
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.itemCode = "";
      this.queryParams.itemName = "";
      this.queryParams.selectedClassId = "";
      // this.resetForm("form");
      this.getlistItem();

      // this.resetForm("formItem");
    },
    /** 重置检验项目按钮操作 */
    // resetQueryitrem() {
    //   console.log("this.resetQueryitrem");
    //   this.resetItem("formItem");
    //   this.handleQueryItem();
    // },

    // 多选框选中数据
    handleSelectionChange(selection) {
      console.log(selection, "fsee");
      this.ids = selection.map((item) => item.id);
      this.items = selection.map((item) => item.itemCode);
      //多选框选中的list
      this.selectedList = selection;
      // this.templateInfoList = selection.map((item) => item);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加检验模板";
      this;
    },
    /** 新增详细按钮操作 */
    handleAddInfo() {
      this.openTemplate = true;
      this.title = "加项目到模板";
      this.getlistItem();
      this.getListClass();
      this.templateInfoList = this.infoList;
    },
    /** 修改按钮操作 */
    append(row) {
      this.reset();
      const id = row.id || this.ids;
      getTemplate(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改检验模板";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTemplate(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTemplate(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 提交按钮 */
    submittamplate() {
      if (this.templateInfoList.length == 0) {
        this.$message({
          message: "请至少添加一条项目模板",
          type: "warning",
        });
        return;
      }
      let form = {
        templateInfoList: this.templateInfoList,
      };
      addInfo(form).then((response) => {
        this.$modal.msgSuccess("新增成功");
        this.openTemplate = false;
        this.getList();
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const items = row.itemCode || this.items
      this.$modal
        .confirm('是否确认删除检项目编号为"' + items + '"的数据项？')
        .then(function () {
          return delInfo(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "quality/template/export",
        {
          ...this.queryParams,
        },
        `检验模板_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
<style lang="scss">

// 表单外围虚线框
.dashed-container {
  border: 1px dashed #ccc;
  padding: 8px 24px;            /* 更大的内边距，适配表单内容 */
  border-radius: 8px;       /* 更大的圆角 */
  background: #fff;         /* 白色背景，与虚线对比更明显 */
  color: black;
  background-color: #fff;
  display: flex;
  justify-content: center;    /* 水平居中 */
  margin-left: 50px;
  margin-top: 10px;
}
</style>