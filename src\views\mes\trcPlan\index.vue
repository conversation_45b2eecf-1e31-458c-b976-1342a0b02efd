<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="工单编号" prop="woNo">
          <el-input
            v-model="queryParams.woNo"
            placeholder="请输入工单编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="计划单编号" prop="orderNo">
          <el-input
            v-model="queryParams.orderNo"
            placeholder="请输入计划单编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="产品编码" prop="bomCode">
          <el-input
            v-model="queryParams.bomCode"
            placeholder="请输入产品编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="工单状态" prop="orderState">
          <el-input
            v-model="queryParams.orderState"
            placeholder="请输入工单状态"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['mes:trcPlan:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['mes:trcPlan:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['mes:trcPlan:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['mes:trcPlan:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="trcPlanList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" prop="index" />
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="工单编号" align="center" prop="woNo" />
        <el-table-column label="计划单编号" align="center" prop="orderNo" />
        <el-table-column label="产品编码" align="center" prop="bomCode" />
        <el-table-column label="产品名称" align="center" prop="bomName" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column
          label="排产开始日期"
          align="center"
          prop="scheduleDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.scheduleDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="排产结束日期"
          align="center"
          prop="scheduleEndDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.scheduleEndDate, "{y}-{m}-{d}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="工单状态" align="center" prop="orderState" />
        <!-- <el-table-column label="工艺路径编码" align="center" prop="routeCode" /> -->
        <el-table-column label="工艺路径名称" align="center" prop="routeName" />
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="修改时间"
          align="center"
          prop="updateDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['mes:trcPlan:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['mes:trcPlan:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 工单编号选择对话框 -->
      <el-dialog
        :title="woDialogTitle"
        :visible.sync="woDialogVisible"
        width="80%"
        append-to-body
      >
        <!-- 搜索表单 -->
        <el-form
          :model="woQueryParams"
          ref="woQueryForm"
          :inline="true"
          size="small"
        >
          <el-form-item label="工单编号" prop="woNo">
            <el-input
              v-model="woQueryParams.woNo"
              placeholder="请输入工单编号"
              clearable
              @keyup.enter.native="handleWoQuery"
            />
          </el-form-item>
          <el-form-item label="工单类型" prop="woType">
            <el-select
              v-model="woQueryParams.woType"
              placeholder="请选择工单状态"
              clearable
            >
              <el-option
                v-for="dict in dict.type.wo_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleWoQuery"
              >搜索</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetWoQuery"
              >重置</el-button
            >
          </el-form-item>
        </el-form>

        <!-- 工单列表 -->
        <el-table
          ref="woTable"
          v-loading="woLoading"
          :data="woList"
          @row-click="handleRowClick"
          highlight-current-row
        >
          <el-table-column type="index" width="55" align="center" label="#" />
          <!-- <el-table-column type="selection" width="55" align="center" /> -->
          <el-table-column label="工单编号" align="center" prop="woNo" />
          <el-table-column
            label="工单类型"
            align="center"
            prop="woType"
            min-width="100"
          >
            <template slot-scope="scope">
              <dict-tag
                :options="dict.type.wo_type"
                :value="scope.row.woType"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="物料编码"
            align="center"
            prop="materialCode"
            min-width="120"
          />
          <el-table-column
            label="物料名称"
            align="center"
            prop="materialName"
            min-width="120"
          />
          <el-table-column
            label="数量"
            align="center"
            prop="qty"
            min-width="80"
          />
          <el-table-column
            label="工艺路径编码"
            align="center"
            prop="routeCode"
            min-width="120"
          />
          <el-table-column
            label="工艺路径名称"
            align="center"
            prop="routeName"
            min-width="120"
          />
          <el-table-column
            label="产线编码"
            align="center"
            prop="lineCode"
            min-width="100"
          />
          <el-table-column
            label="产线名称"
            align="center"
            prop="lineName"
            min-width="120"
          />
          <el-table-column
            label="计划开始时间"
            align="center"
            prop="planStartTime"
            min-width="160"
          >
            <template slot-scope="scope">
              {{ parseTime(scope.row.planStartTime) }}
            </template>
          </el-table-column>
          <el-table-column
            label="计划结束时间"
            align="center"
            prop="planEndTime"
            min-width="160"
          >
            <template slot-scope="scope">
              {{ parseTime(scope.row.planEndTime) }}
            </template>
          </el-table-column>
        </el-table>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="confirmWoSelection"
            >确 定</el-button
          >
          <el-button @click="cancelWoSelection">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 添加或修改计划排产对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="工单信息" name="1">
              <el-row :gutter="24">
                <el-col :span="6">
                  <el-form-item label="工单编号" prop="woNo">
                    <el-input
                      v-model="form.woNo"
                      placeholder="请选择工单编号"
                      readonly
                      @click.native="openWoDialog"
                      style="cursor: pointer"
                    >
                      <i slot="suffix" class="el-icon-arrow-down"></i>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="产品编号" prop="productNo">
                    <el-input
                      v-model="form.productNo"
                      placeholder="请输入产品编号"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="工艺路径编码" prop="routeCode">
                    <el-input
                      v-model="form.routeCode"
                      placeholder="请输入工艺路径编码"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="BOM编码" prop="bomCode">
                    <el-input
                      v-model="form.bomCode"
                      placeholder="请输入BOM编码"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24">
                <el-col :span="6">
                  <el-form-item
                    label="交货日期"
                    prop="interactionDate"
                    style="width: 150px"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.interactionDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择交货日期"
                      style="width: 130%"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="工单类型" prop="woType">
                    <el-select
                      v-model="form.woType"
                      placeholder="请选择工单类型"
                      clearable
                    >
                      <el-option
                        v-for="dict in dict.type.wo_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="产线编码" prop="lineCode">
                    <el-input
                      v-model="form.lineCode"
                      placeholder="请输入产线编码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="产线名称" prop="lineName">
                    <el-input
                      v-model="form.lineName"
                      placeholder="请输入产线名称"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>

            <el-collapse-item title="工单排产" name="2">
              <el-row :gutter="24">
                <el-col :span="6">
                  <el-form-item
                    label="计划单编号"
                    prop="orderNo"
                    style="width: 200px"
                  >
                    <el-input
                      v-model="form.orderNo"
                      placeholder="请输入计划单编号"
                      disabled
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="数量" prop="qty" style="width: 200px">
                    <el-input v-model="form.qty" placeholder="请输入数量" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    label="排产开始日期"
                    prop="scheduleDate"
                    style="width: 150px"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.scheduleDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择排产开始日期"
                      style="width: 130%"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    label="排产结束日期"
                    prop="scheduleEndDate"
                    style="width: 150px"
                  >
                    <el-date-picker
                      clearable
                      v-model="form.scheduleEndDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择排产结束日期"
                      style="width: 130%"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="备注" prop="remark" style="width: 240px">
                  <el-input v-model="form.remark" placeholder="请输入备注" />
                </el-form-item>
              </el-row>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listTrcPlan,
  getTrcPlan,
  delTrcPlan,
  addTrcPlan,
  updateTrcPlan,
} from "@/api/mes/trcPlan";
import { listTrcOrderWork } from "@/api/mes/trcOrderWork";

export default {
  name: "TrcPlan",
  dicts: ["wo_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 折叠面板展开状态
      activeNames: ["1", "2"],
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 计划排产表格数据
      trcPlanList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 工单选择对话框相关数据
      woDialogVisible: false,
      woDialogTitle: "选择工单编号",
      woLoading: false,
      woList: [],
      selectedWoData: null,
      woQueryParams: {
        pageNum: 1,
        pageSize: 10,
        woNo: null,
        materialCode: null,
        woType: null,
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        woId: null,
        woNo: null,
        orderNo: null,
        bomCode: null,
        bomName: null,
        bomId: null,
        bomVersion: null,
        qty: null,
        scrapQty: null,
        bgslQty1: null,
        bgslQty: null,
        badnessQty: null,
        workQty: null,
        finishQty: null,
        scheduleDate: null,
        scheduleEndDate: null,
        orderState: null,
        routeId: null,
        routeCode: null,
        routeName: null,
        routeVersion: null,
        createDate: null,
        updateDate: null,
        //工单类型
        woType: null,
        lineId: null,
        lineCode: null,
        lineName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        woId: [{ required: true, message: "工单id不能为空", trigger: "blur" }],
        woNo: [
          { required: true, message: "工单编号不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询计划排产列表 */
    getList() {
      this.loading = true;
      listTrcPlan(this.queryParams).then((response) => {
        this.trcPlanList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        woId: null,
        woNo: null,
        orderNo: null,
        bomCode: null,
        bomName: null,
        bomId: null,
        bomVersion: null,
        qty: null,
        scrapQty: null,
        bgslQty1: null,
        bgslQty: null,
        badnessQty: null,
        workQty: null,
        finishQty: null,
        scheduleDate: null,
        scheduleEndDate: null,
        orderState: null,
        routeId: null,
        routeCode: null,
        routeName: null,
        routeVersion: null,
        remark: null,
        createBy: null,
        createDate: null,
        updateBy: null,
        updateDate: null,
        delFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.activeNames = ["1", "2"];
      this.open = true;
      this.title = "添加计划排产";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getTrcPlan(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改计划排产";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTrcPlan(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTrcPlan(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除计划排产编号为"' + ids + '"的数据项？')
        .then(function () {
          return delTrcPlan(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "mes/trcPlan/export",
        {
          ...this.queryParams,
        },
        `计划排产_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    /** 打开工单选择对话框 */
    openWoDialog() {
      this.woDialogVisible = true;
      this.getWoList();
    },
    /** 获取工单列表 */
    getWoList() {
      this.woLoading = true;
      listTrcOrderWork(this.woQueryParams).then((response) => {
        this.woList = response.rows;
        this.woLoading = false;
      });
    },
    /** 工单查询按钮操作 */
    handleWoQuery() {
      this.woQueryParams.pageNum = 1;
      this.getWoList();
    },
    /** 重置工单查询 */
    resetWoQuery() {
      this.resetForm("woQueryForm");
      this.handleWoQuery();
    },

    /** 行点击事件处理 */
    handleRowClick(row) {
      console.log("选中的工单数据:", row);
      this.$refs.woTable.setCurrentRow(row);
      this.selectedWoData = row;
    },
    /** 确认工单选择 */
    confirmWoSelection() {
      if (!this.selectedWoData) {
        this.$modal.msgError("请选择一条工单数据");
        return;
      }

      // 将选中的工单数据赋值到表单对应字段
      this.form = {
        ...this.form, // 保留表单其他字段的值
        woNo: this.selectedWoData.woNo, // 工单编号
        materialCode: this.selectedWoData.materialCode, // 产品编码
        routeCode: this.selectedWoData.routeCode, // 工艺路径编码
        bomCode: this.selectedWoData.bomCode, // BOM编码
        deliveryDate: this.selectedWoData.deliveryDate, // 交货日期
        woType: this.selectedWoData.woType, // 工单类型
        lineCode: this.selectedWoData.lineCode, // 产线编码
        lineName: this.selectedWoData.lineName, // 产线名称
      };

      this.woDialogVisible = false;
      this.$refs.woTable.clearSelection();
    },
    /** 取消工单选择 */
    cancelWoSelection() {
      this.woDialogVisible = false;
      this.$refs.woTable.setCurrentRow(null);
      this.selectedWoData = null;
    },
  },
};
</script>
