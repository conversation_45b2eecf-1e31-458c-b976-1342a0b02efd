import request from '@/utils/request'

// 查询wms库存列表
export function listInventory(query) {
  return request({
    url: '/system/inventory/list',
    method: 'get',
    params: query
  })
}

// 查询wms库存列表
export function listInventoryGroup(query) {
  return request({
    url: '/system/inventory/listGroup',
    method: 'get',
    params: query
  })
}

// 查询wms库存总览列表
export function listInventoryall(query) {
  return request({
    url: '/system/inventory/all',
    method: 'get',
    params: query
  })
}

// 查询wms库存盘点列表
export function listInventorygp(query) {
  return request({
    url: '/system/inventory/gp',
    method: 'get',
    params: query
  })
}

// 查询wms库存详细
export function getInventory(id) {
  return request({
    url: '/system/inventory/' + id,
    method: 'get'
  })
}

// 新增wms库存
export function addInventory(data) {
  return request({
    url: '/system/inventory',
    method: 'post',
    data: data
  })
}

// 修改wms库存
export function updateInventory(data) {
  return request({
    url: '/system/inventory',
    method: 'put',
    data: data
  })
}

// 删除wms库存
export function delInventory(id) {
  return request({
    url: '/system/inventory/' + id,
    method: 'delete'
  })
}

// 根据仓库去查
export function listInventoryByWarehose(query) {
  return request({
    url: '/system/inventory/listByWarehose',
    method: 'get',
    params: query
  })
}
