<template>
  <div class="app-container">
    <div class="app-container-div">
      <!-- 明细添加物料对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >
        <el-form :data="materialList" :inline="true" label-width="100px">
          <el-form-item label="物料编码" prop="materialCode">
            <el-input
              v-model="queryParamsMaterial.materialCode"
              placeholder="请输入物料编码"
              clearable
              @keyup.enter.native="handleQueryMaterial"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQueryMaterial"
              >搜索</el-button
            >
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQueryMaterial"
              >重置</el-button
            >
          </el-form-item>
          <el-table
            ref="materialTable"
            :data="materialList"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChangematerial"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column
              label="物料编码"
              align="center"
              prop="materialCode"
            />
            <el-table-column
              label="物料名称"
              align="center"
              prop="materialName"
            />
            <el-table-column
              label="规格型号"
              align="center"
              prop="specification"
            />
            <el-table-column label="单位" align="center" prop="materialUnit" />
          </el-table>
        </el-form>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadListMaterials"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleAddMaterial">确 定</el-button>
        </span>
      </el-dialog>
      <!-- 添加或修改箱信息对话框 -->
      <el-form ref="form" :model="form" :rules="rules">
        <el-row>
          <el-col :span="6">
            <el-form-item
              label="物料编码"
              prop="materialCode"
              style="width: 260px"
            >
              <el-input
                v-model="form.materialCode"
                placeholder="请输入物料编码"
                @click.native="handleAddWmsMoveDetail"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="物料名称"
              prop="materialName"
              style="width: 260px"
            >
              <el-input
                v-model="form.materialName"
                placeholder="请输入物料名称"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="批次" prop="batchNo" style="width: 260px">
              <el-input
                v-model="form.batchNo"
                placeholder="请输入批次"
                
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item label="生产日期" prop="dateCode" style="width: 260px">
              <el-date-picker
                clearable
                v-model="form.dateCode"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择生产日期"
                style="width: 260px"
                :picker-options="pickerStartTime"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item
              label="订单分类"
              prop="orderClass"
              style="width: 260px"
            >
              <el-select
                v-model="form.orderClass"
                placeholder="请选择订单状态"
                clearable
                style="width: 260px"
              >
                <el-option
                  v-for="dict in dict.type.order_class"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item
              label="保质期"
              prop="expirationDate"
              style="width: 260px"
            >
              <el-date-picker
                clearable
                v-model="form.expirationDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择保质期"
                style="width: 260px"
                :picker-options="pickerEndTime"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="6">
            <el-form-item
              label="是否为成品"
              prop="boxState"
              style="width: 260px"
            >
              <el-select
                v-model="form.isProduction"
                placeholder="请选择是否为成品"
                clearable
                style="width: 260px"
              >
                <el-option
                  v-for="dict in dict.type.is_production"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="数量" prop="qty" style="width: 260px">
              <el-input-number
                v-model="form.qty"
                :min="1"
                label="请输入数量"
                style="width: 260px"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="打印数量" prop="printQty" style="width: 260px">
              <el-input-number
                v-model="form.printQty"
                :min="1"
                label="请输入打印数量"
                style="width: 260px"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6"> </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <el-form-item
              label="规格型号"
              prop="specification"
              style="width: 260px"
            >
              <el-input
                disabled
                v-model="form.specification"
                placeholder="请输入规格型号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="单位" prop="materialUnit" style="width: 260px">
              <el-input
                v-model="form.materialUnit"
                placeholder="请输入单位"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态" prop="boxState" style="width: 260px">
              <el-select
                v-model="form.boxState"
                placeholder="请选择状态"
                clearable
                style="width: 260px"
                disabled
              >
                <el-option
                  v-for="dict in dict.type.box_state"
                  :key="dict.id"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6"> </el-col>
        </el-row>
      </el-form>
      <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { listHiprint } from "@/api/system/hiprint";
import { listBox, getBox, delBox, addBox, updateBox } from "@/api/system/box";
import { listPurchase_detail } from "@/api/system/purchase_detail";
import { Loading } from "element-ui";
import { hiprint, defaultElementTypeProvider } from "vue-plugin-hiprint";
import { listMaterial } from "@/api/system/material";

export default {
  dicts: ["box_state", "order_class", "line_state_dict", "is_production"],
  name: "Box",
  data() {
    return {
      showMoreConditions: false, // 控制是否显示更多条件

      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,

      // 箱信息表格数据
      boxList: [],
      // 采购单表格数据
      purchaseList: [],
      // 遮罩层
      loading: true,
      dialogVisible: false,
      //选择框选中数据
      selectPurchaselist: [],
      // 总条数
      total: 0,
      detailtotal: 0,
      // 采购单明细表格数据
      wmsErpPurchaseDetailList: [],
      // 遮罩层
      detailloading: true,
      detaildialogVisible: false,
      //选择框选中数据
      selectPurchaseDetaillist: [],
      // 物料单明细表格数据
      moveDetailList: [],
      //获取物料列表
      materialList: [],
      // 选中的物料数据
      detailmaterialList: [],
      // 总条数
      detailtotal: 0,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: null,
        batchNo: null,
        comId: null,
        orderClass: null,
        isproduction: null,
      },
      // 物料查询参数
      queryParamsMaterial: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
      },
      queryParamsDeatil: {
        materialCode: null,
        materialName: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        purchaseNo: [
          { required: true, message: "采购单号不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        printQty: [
          { required: true, message: "打印数量不能为空", trigger: "blur" },
        ],
        supplierCode: [
          { required: true, message: "供应商不能为空", trigger: "blur" },
        ],
        batchNo: [{ required: true, message: "批次不能为空", trigger: "blur" }],
        dateCode: [
          { required: true, message: "生产日期不能为空", trigger: "blur" },
        ],
        expirationDate: [
          { required: true, message: "保质期不能为空", trigger: "blur" },
        ],
      },
      mypanel: {},
      hiprintList: [],
    };
  },
  created() {
    this.reset();
    this.getList();
    this.getPrintList();
    this.loadListMaterials();
  },
  computed: {
    pickerStartTime() {
      let _this = this;

      return {
        disabledDate: (time) => {
          if (_this.form.expirationDate) {
            let expirationDate = _this.form.expirationDate.replace(/-/g, "/");

            return time.getTime() >= new Date(expirationDate);
          }
        },
      };
    },

    pickerEndTime() {
      let _this = this;

      return {
        disabledDate: (time) => {
          if (_this.form.dateCode) {
            let dateCode = _this.form.dateCode.replace(/-/g, "/");

            return time.getTime() <= new Date(dateCode);
          }
        },
      };
    },
  },
  methods: {
    getPrintList() {
      this.loading = true;
      listHiprint({ code: "box" }).then((response) => {
        this.form.printId = response.rows[0].id;
        this.mypanel = JSON.parse(response.rows[0].printJson);
        this.hiprintList = response.rows;
      });
    },
    toggleMoreConditions() {
      this.showMoreConditions = !this.showMoreConditions; // 切换显示状态
    },
    /** 查询箱信息列表 */
    getList() {
      this.loading = true;
      listBox(this.queryParams).then((response) => {
        this.boxList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        boxNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        dateCode: null,
        expirationDate: null,
        parentBoxId: null,
        parentBoxNo: null,
        qty: null,
        specification: null,
        materialUnit: null,
        boxState: "CREATED",
        batchNo: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        orderClass: "mass",
        printQty: 1,
      };
      this.resetForm("form");
    },
    /**添加物料 */
    handleAddWmsMoveDetail() {
       console.log('点击了物料编码输入框');
      this.dialogVisible = true;
      this.title = "物料选择";
      // 清空选中的物料数据
      this.detailmaterialList = [];
      this.materialList = [];
      this.loadListMaterials();
      this.ids = [];
      // 清除表格选中状态
      this.$nextTick(() => {
        const tableRef = this.$refs.materialTable; // 获取表格的 ref
        if (tableRef) {
          tableRef.clearSelection(); // 清除选中状态
        }
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      console.log("表单", this.queryParams);

      this.resetForm("queryForm");
      this.getPurchaseList();
    },
    handleClose() {
      this.dialogVisible = false;
      // 清除表格选中状态
      this.$nextTick(() => {
        const tableRef = this.$refs.materialTable; // 获取表格的 ref
        if (tableRef) {
          tableRef.clearSelection(); // 清除选中状态
        }
        this.loading = false; // 重置加载状态
      });

      // 清空选中的物料数据
      this.detailmaterialList = [];
      this.ids = [];
      this.resetQueryMaterial();
    },
            /** 重置物料查询按钮操作 */
    resetQueryMaterial() {
      this.resetForm("queryFormMaterial");
      // 重置查询参数
      this.queryParamsMaterial = {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
      };

      // 清空选中的物料数据
      this.detailmaterialList = [];

      // 清空表格数据
      this.materialList = [];

      this.handleQueryMaterial();
    },
    handleSelectionChangematerial(selection) {
      console.log("当前选中的数据：", selection); // 打印选中的数据
      console.log("当前materiallist：", this.materialList); // 打印选中的数据长度
      this.ids = selection.map((item) => item.id);
      console.log("选中的ID列表：", this.ids);
      // 获取选中项的ID数组

      // 根据ID筛选需要提交的数据
      this.detailmaterialList = this.materialList.filter((item) =>
        this.ids.includes(item.id)
      );
      console.log("筛选后的提交数据：", this.detailmaterialList);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加箱信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getBox(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改箱信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let loadingInstance = Loading.service({
            fullscreen: true,
            target: document.querySelector(".el-main"),
            text: "打印中...",
            background: "rgba(0, 0, 0, 0.8)",
          });
          addBox(this.form).then((response) => {
            this.$modal.msgSuccess("打印成功");
            for (let i = 0; i < response.data.length; i++) {
              const item = response.data[i];
              console.log(item, "item--");
              this.$nextTick(() => {
                // 以服务的方式调用的 Loading 需要异步关闭
                loadingInstance.close();

                //进行pdf打印

                hiprint.init();
                //调用接口获取数据
                var hiprintTemplate = new hiprint.PrintTemplate({
                  template: this.mypanel,
                  settingContainer: "#templateDesignDiv",
                });
                this.form.qrCode = item;
                this.form.boxNo = item;
                hiprintTemplate.print([this.form]);
              });
            }
            this.reset();
          });
        } else {
          // 验证失败时不执行任何操作
          // Element UI 会自动显示错误信息
          console.log("表单验证失败，请检查必填项");
          return false; // 阻止后续操作
        }
      });
    },
    handleAddMaterial() {
      console.log("确定的时候this.detailmaterialList", this.detailmaterialList);
      if (this.detailmaterialList.length === 0) {
        this.$modal.msgError("请至少选择一条物料数据");
        return; // 阻止提交
      }

      const selectedMaterial = this.detailmaterialList[0];

      // 将选中的物料数据赋值给主表单
      this.form = {
        ...this.form,
        materialId: selectedMaterial.id,
        materialCode: selectedMaterial.materialCode,
        materialName: selectedMaterial.materialName,
        specification: selectedMaterial.specification,
        materialUnit: selectedMaterial.materialUnit,
        boxState: "CREATED", // 设置默认状态
        isProduction: selectedMaterial.isProduction || "0", // 设置是否成品
        orderClass: this.form.orderClass || "mass", // 保持原有值或设置默认值
        printQty: this.form.printQty || 1, // 保持原有值或设置默认值
        qty: this.form.qty || 1, // 保持原有值或设置默认值
      };

      // 关闭对话框
      this.dialogVisible = false;

      this.$refs.materialTable.clearSelection();
      this.detailmaterialList = [];
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除箱信息编号为"' + ids + '"的数据项？')
        .then(function () {
          return delBox(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/box/export",
        {
          ...this.queryParams,
        },
        `箱信息_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    rowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // 确保只能选择一行数据
      if (selection.length > 1) {
        // 只保留最后选中的那一行
        const lastSelected = selection[selection.length - 1];
        this.$refs.materialTable.clearSelection();
        this.$refs.materialTable.toggleRowSelection(lastSelected, true);
        this.detailmaterialList = [lastSelected];
      } else {
        this.detailmaterialList = selection;
      }

      // 更新状态
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    onSelectAll() {
      this.$refs.multipleTable.clearSelection();
    },
    purchaseDetailSubmit() {
      if (this.selectPurchaseDetaillist.length == 0) {
        this.$message({
          message: "请选择数据",
          type: "warning",
        });
        return;
      }
      let row = this.selectPurchaseDetaillist[0];
      this.detaildialogVisible = false;
      this.form.materialCode = row.partCode;
      this.form.materialName = row.partName;
      this.form.materialId = row.partId;
      this.form.materialUnit = row.uom;
      this.form.specification = row.partSpecification;
      console.log("被电击了", row, this.selectPurchaseDetaillist);
    },
    rowDetailClick(row) {
      this.$refs.detailTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    // 多选框选中数据
    handleSelectionDetail(selection) {
      console.log(selection);
      if (selection.length > 1) {
        var newRows = selection.filter((it, index) => {
          if (index == selection.length - 1) {
            this.$refs.detailTable.toggleRowSelection(it, true); //这行可以不要
            return true;
          } else {
            this.$refs.detailTable.toggleRowSelection(it, false);
            return false;
          }
        });
        this.selectPurchaseDetaillist = newRows;
      } else {
        this.selectPurchaseDetaillist = selection;
      }
      console.log(
        this.selectPurchaseDetaillist,
        "this.selectPurchaseDetaillist"
      );
    },
    onSelectDetailAll() {
      this.$refs.detailTable.clearSelection();
    },
    loadListMaterials() {
      listMaterial(this.queryParamsMaterial).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        console.log("this.materialList", this.materialList);
      });
    },
    /** 查询采购单明细列表 */
    detail_list() {
      let queryParams = {
        pageNum: 1,
        pageSize: 10,
        purchaseNo: this.form.purchaseNo,
      };
      listPurchase_detail(queryParams).then((response) => {
        this.wmsErpPurchaseDetailList = response.rows;
        this.detailtotal = response.total;
        console.log("结果", response);
      });
    },
    /** 查询物料列表 */
    handleQueryMaterial() {
      this.queryParamsMaterial.pageNum = 1;
      this.getlistMaterial();
    },
    getlistMaterial() {
      this.loading = true;
      listMaterial(this.queryParamsMaterial).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        //console.log("this.materialList", this.materialList);
      });
    },
    //供应商选择改变的时候
    supplierNameChange(item) {
      console.log("item:", item);
      this.form.supplierCode = item.supplierCode;
      this.form.supplierName = item.supplierName;
    },
    /** 查询 供应商管理列表 */
    getSupplierList() {
      listSupplier(this.queryParams).then((response) => {
        this.supplierList = response.rows;
        console.log("rows:", this.supplierList);
      });
    },
  },
};
</script>
