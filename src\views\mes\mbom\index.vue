<template>
  <div class="app-container">
    <div class="app-container-div">
      <div style="padding: 0px;background: #fff; border-radius: 5px">
        <el-row :gutter="20">
          <el-col :span="5" :xs="24" v-loading="treeLoading">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['system:mbom:add']">添加产品BOM</el-button>
            <div class="head-container">
              <el-input placeholder="输入关键字进行过滤" v-model="filterText"></el-input>
            </div>
            <el-tree :data="listTree" node-key="id" :props="defaultProps" @node-click="handleNodeClick"
              :filter-node-method="filterNode" default-expand-all :expand-on-click-node="false"
              :highlight-current="true" ref="tree">
              <template #default="{ node, data }">
                <div class="tree-node-custom">
                  <span>{{ node.label }}</span>
                  <el-dropdown trigger="click" @command="(cmd) => handleTreeCommand(data, cmd)">
                    <span class="el-dropdownlink">
                      <i class="el-icon-more"></i>
                    </span>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="edit">编辑</el-dropdown-item>
                      <el-dropdown-item command="delete">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </template>
            </el-tree>
          </el-col>
          <el-col :span="19" :xs="24">
            <div style="padding: 10px; 
               font-size: 14px;
               font-weight: normal;
               color: #606266;
               display: flex;
               justify-content: space-around;
               ">
              <span>物料编码：{{ parentData.code }}</span>
              <span>物料名称：{{ parentData.name }}</span>
            </div>
            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                  v-hasPermi="['system:mbom:add']">添加物料</el-button>
              </el-col>
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>
            <el-table fit :data="mbomList" @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'createTime', order: 'descending' }">
              <el-table-column type="index" width="55" align="center" />
              <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="物料编码" :width="tableWidth(mbomList.map(x => x.materialCode))" align="center"
                prop="materialCode" />
              <el-table-column label="物料名称" align="center" prop="materialName"
                :width="tableWidth(mbomList.map(x => x.materialName))" />
              <el-table-column label="所属工厂" align="center" prop="siteId" />
              <el-table-column label="物料版本" align="center" prop="materialVersion" />
              <el-table-column label="数量" align="center" prop="quantity" />
              <el-table-column label="是否启用替代料" align="center" prop="isUseAlternate" />
              <el-table-column label="版本" align="center" prop="version" />
              <el-table-column label="状态" align="center" prop="status" />
              <el-table-column label="备注" :width="tableWidth(mbomList.map(x => x.remark))" align="center"
                prop="remark" />
              <el-table-column label="操作" fixed="right" :width="tableWidth()" align="center"
                class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                    v-hasPermi="['system:mbom:edit']">修改</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                    v-hasPermi="['system:mbom:remove']">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
              :limit.sync="queryParams.pageSize" @pagination="getList" />
            <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
              <el-form ref="form" :model="form" :rules="rules">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="上一级BOM" prop="parentId" style="width: 240px;">
                      <el-select v-model="form.parentId" placeholder="请选择上一级BOM" style="width: 240px"
                        :disabled="isEditMode">
                        <el-option v-for="item in listTree" :key="item.id" :label="item.label" :value="item.id" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="BOM版本" prop="version" style="width: 240px;">
                      <el-input v-model="form.version" placeholder="请输入BOM版本" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="物料名称" prop="materialName" style="width: 240px;">
                      <el-input v-model="form.materialName" placeholder="请输入物料名称" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="物料编码" prop="materialCode" style="width: 240px;">
                      <el-input v-model="form.materialCode" placeholder="请输入物料编码" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="数量" prop="quantity" style="width: 240px;">
                      <el-input v-model="form.quantity" placeholder="请输入数量" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="单位" prop="unit" style="width: 240px;">
                      <el-input v-model="form.unit" placeholder="请输入单位" />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="所属工厂" prop="siteId" style="width: 240px;">
                      <el-input v-model="form.siteId" placeholder="请输入所属工厂" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="物料版本" prop="materialVersion" style="width: 240px;">
                      <el-input v-model="form.materialVersion" placeholder="请输入物料版本" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="是否启用替代料" prop="isUseAlternate" style="width: 240px;">
                      
                      <el-radio-group v-model="form.isUseAlternate">
                        <el-radio label="true">是</el-radio>
                        <el-radio label="false">否</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
                <!-- <el-form-item label="父节点id" prop="parentId" style="width: 240px;">
                  <el-input v-model="form.parentId" placeholder="请输入父节点id" />
                </el-form-item> -->
                <!-- <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
                  <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
                </el-form-item> -->
                <!-- <el-form-item label="所有父级id" prop="parentIds" style="width: 240px;">
                  <el-input v-model="form.parentIds" placeholder="请输入所有父级id" />
                </el-form-item>
                <el-form-item label="子节点" prop="children" style="width: 240px;">
                  <el-input v-model="form.children" placeholder="请输入子节点" />
                </el-form-item>
                <el-form-item label="节点名称" prop="nodeName" style="width: 240px;">
                  <el-input v-model="form.nodeName" placeholder="请输入节点名称" />
                </el-form-item> -->
              </el-form>
              <div class="demo-drawer__footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
              </div>
            </el-drawer>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { listMbom, getMbom, delMbom, addMbom, updateMbom, getTreeList } from "@/api/mes/mbom";

export default {
  name: "Mbom",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // BOM管理表格数据
      mbomList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        materialId: null,
        materialCode: null,
        materialName: null,
        siteId: null,
        parentId: null,
        status: null,
        quantity: null,
        unit: null,
        parentIds: null,
        children: null,
        nodeName: null
      },
      // 表单参数
      form: {},

      // 表单校验
      rules: {
        isUseAlternate: [
          { required: true, message: "请选择是否启用替代料", trigger: "change" }
        ]
      },
      parentData: {
        code: '请先在左侧进行选择',
        name: '请先在左侧进行选择',
        id: '0',
      },
      treeLoading: false,
      filterText: '',
      level: null,
      listTree: [],
      defaultProps: {
        children: "children",
        label: "label",
      },
      isEditMode: false,
      isUseAlternate: '否',
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    // this.getList();
    this.getTree();
  },
  methods: {
    // 处理树形节点的下拉操作（编辑/删除）
    handleTreeCommand(data, command) {
      if (command === "edit") {
        this.handleTreeEdit(data); // 编辑逻辑
      } else if (command === "delete") {
        this.handleTreeDelete(data); // 删除逻辑
      }
    },
    handleTreeEdit(data) {
      this.reset(); // 重置表单
      // 将节点数据赋值给表单
      console.log("-------", data)
      this.form = {
        id: data.id,
        materialId: data.materialId,
        materialCode: data.materialCode,
        materialName: data.materialName,
        siteId: data.siteId,
        parentId: data.parentId,
        status: data.status,
        quantity: data.quantity,
        unit: data.unit,
        delFlag: data.delFlag,
        parentIds: data.parentIds,
        children: null, // 不复制children数组
        nodeName: data.nodeName
      };
      this.open = true; // 打开抽屉/弹窗
      this.title = "编辑BOM"; // 弹窗标题
      this.isEditMode = true;
    },
    handleTreeDelete(data) {
      this.$modal.confirm(`是否确认删除物料「${data.label}」？`).then(() => {
        // 调用后端删除接口
        return delMbom(data.id);
      }).then(() => {
        this.getTree(); // 删除后重新加载树形数据
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 查询BOM管理列表 */
    getList() {

      // 添加排序参数
      const params = {
        ...this.queryParams,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      };
      this.queryParams.id = this.parentData.id;
      listMbom(this.queryParams).then((response) => {
        this.mbomList = response.rows;
        this.total = response.total;

      });
    },

    //获取树结构列表
    getTree() {
      this.treeLoading = true;
      getTreeList().then((response) => {
        this.listTree = response;
        this.treeLoading = false;
      });
    },

    handleNodeClick(e) {
      this.level = e.level;//控制v-if的 勿删
      this.parentData.id = e.id;
      this.parentData.code = e.code;
      this.parentData.name = e.label;
      this.getList();
      //默认界面 
      //e.id 为本节点id
      //
      // if (e.level == "LEVEL_1") { 
      //   this.queryParams.parentId = e.id;
      //   this.getList();
      //   // this.queryParams.parentId = null;
      // }else if (e.level == "LEVEL_2") { 
      //   // this.queryParams.parentId = e.id;
      //   this.getList();
      //   this.queryParams.parentId = null;
      // }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        siteId: null,
        parentId: this.parentData.id,
        status: null,
        quantity: null,
        unit: null,
        delFlag: null,
        parentIds: null,
        children: null,
        nodeName: null
      };
      this.isEditMode = false; // 重置编辑模式
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.parentId = this.parentData.id;//设置当前选中的父物料为默认值
      this.isEditMode = false; // 新增模式
      this.open = true;
      this.title = "添加BOM管理";
      this.getTree(); // 重新加载树形数据
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getMbom(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改BOM管理";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMbom(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.isEditMode = false; // 重置编辑模式
              this.getList();
              this.getTree(); // 重新加载树形数据以反映更改
            });
          } else {
            addMbom(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.isEditMode = false; // 重置编辑模式
              this.getList();
              this.getTree(); // 重新加载树形数据以反映更改
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除BOM管理编号为"' + ids + '"的数据项？').then(function () {
        return delMbom(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mes/mbom/export', {
        ...this.queryParams
      }, `BOM管理_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
