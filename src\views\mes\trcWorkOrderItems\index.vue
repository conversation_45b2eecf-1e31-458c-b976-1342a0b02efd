<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="工单编号" prop="woNo">
          <el-input
            v-model="queryParams.woNo"
            placeholder="请输入工单编号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="物料id" prop="materialId">
        <el-input
          v-model="queryParams.materialId"
          placeholder="请输入物料id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item label="物料编码" prop="materialCode">
          <el-input
            v-model="queryParams.materialCode"
            placeholder="请输入物料编码"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="物料名称" prop="materialName">
          <el-input
            v-model="queryParams.materialName"
            placeholder="请输入物料名称"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <!-- <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['mes:trcOrderWork:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['mes:trcOrderWork:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['mes:trcOrderWork:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['mes:trcOrderWork:export']"
            >导出</el-button
          >
        </el-col>
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row> -->

      <el-table
        height="42vh"
        v-loading="loading"
        :data="trcOrderWorkList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
      >
        <el-table-column label="工单编号" align="center" prop="woNo" />
        <el-table-column label="工艺路径名称" align="center" prop="routeName" />
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="BOM名称" align="center" prop="bomName" />
        <el-table-column label="BOM版本" align="center" prop="bomVersion" />
        <el-table-column label="计划数量" align="center" prop="bomVersion" />
        <el-table-column
          label="状态"
          align="center"
          prop="currentStateName"
        />
        <el-table-column label="修改人" align="center" prop="updateBy" />
        <el-table-column
          label="修改时间"
          align="center"
          prop="updateTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['mes:trcOrderWork:edit']"
              >修改1231</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['mes:trcOrderWork:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column> -->
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
      <el-table
        height="42vh"
        v-loading="loadingItem"
        :data="trcWorkOrderItemsList"
      >
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column
          label="物料版本"
          align="center"
          prop="materialVersion"
        />
        <el-table-column label="消耗数量" align="center" prop="qty" />
        <el-table-column label="单位" align="center" prop="unit" />
        <el-table-column
          label="是否启用替代料"
          align="center"
          prop="useAlternate"
        />
        <el-table-column label="物料校验" align="center" prop="forceCheck" />
        <el-table-column label="总消耗数量" align="center" prop="qtyTotal" />
        <el-table-column
          label="替代组"
          align="center"
          prop="alternativeGroup"
        />
        <!-- <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['mes:trcWorkOrderItems:edit']"
              >修改</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['mes:trcWorkOrderItems:remove']"
              >删除</el-button
            >

            >
          </template>
        </el-table-column> -->
      </el-table>
    </div>
  </div>
</template>

<script>
import {
  listTrcOrderWork,
  getTrcOrderWork,
  delTrcOrderWork,
  addTrcOrderWork,
  updateTrcOrderWork,
} from "@/api/mes/trcOrderWork";
import {
  listTrcWorkOrderItems,
  getTrcWorkOrderItems,
  delTrcWorkOrderItems,
  addTrcWorkOrderItems,
  updateTrcWorkOrderItems,
} from "@/api/mes/trcWorkOrderItems";
export default {
  name: "TrcOrderWork",
  dicts: ["workorder_state"],
  data() {
    return {
      // 遮罩层
      loading: true,
      loadingItem: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工单管理表格数据
      trcOrderWorkList: [],
      // 物料需求子表查询
      trcWorkOrderItemsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        woNo: null,
        woType: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialState: null,
        lineId: null,
        lineCode: null,
        lineName: null,
        productSchedule: null,
        workorderState: null,
        planStartDate: null,
        planEndDate: null,
        woFinishDate: null,
        parentNo: null,
        woName: null,
        workorderStateArr: [],
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    handleRowClick(row) {
      console.log(row.id);
      this.getListTrcWorkOrderItems(row.id);
    },
    /** 查询工单管理列表 */
    getList() {
      this.loading = true;
      listTrcOrderWork(this.queryParams).then((response) => {
        this.trcOrderWorkList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getListTrcWorkOrderItems(id) {
      const params = {
        woId: id,
      };
      this.loadingItem = true;
      listTrcWorkOrderItems(params).then((response) => {
        this.trcWorkOrderItemsList = response.rows;
        // this.total = response.total;
        this.loadingItem = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        woNo: null,
        woType: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialState: null,
        lineId: null,
        lineCode: null,
        lineName: null,
        productSchedule: null,
        workorderState: null,
        planStartDate: null,
        planEndDate: null,
        woFinishDate: null,
        parentNo: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        woName: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      console.log(ids, "ids");
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工单管理";
    },
    /** 修改按钮操作 */

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id != null) {
            updateTrcOrderWork(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            console.log(this.form);
            addTrcOrderWork(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除工单管理编号为"' + ids + '"的数据项？')
        .then(function () {
          return delTrcOrderWork(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "mes/trcOrderWork/export",
        {
          ...this.queryParams,
        },
        `工单管理_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
