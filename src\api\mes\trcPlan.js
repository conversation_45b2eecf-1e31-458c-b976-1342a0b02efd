import request from '@/utils/request'

// 查询计划排产列表
export function listTrcPlan(query) {
  return request({
    url: '/mes/trcPlan/list',
    method: 'get',
    params: query
  })
}

// 查询计划排产详细
export function getTrcPlan(id) {
  return request({
    url: '/mes/trcPlan/' + id,
    method: 'get'
  })
}

// 新增计划排产
export function addTrcPlan(data) {
  return request({
    url: '/mes/trcPlan',
    method: 'post',
    data: data
  })
}

// 修改计划排产
export function updateTrcPlan(data) {
  return request({
    url: '/mes/trcPlan',
    method: 'put',
    data: data
  })
}

// 删除计划排产
export function delTrcPlan(id) {
  return request({
    url: '/mes/trcPlan/' + id,
    method: 'delete'
  })
}
