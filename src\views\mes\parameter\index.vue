<template>
  <div class="app-container">
      <div class="app-container-div">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标准值" prop="standardValue">
        <el-input
          v-model="queryParams.standardValue"
          placeholder="请输入标准值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="下限值" prop="lowerLimitValue">
        <el-input
          v-model="queryParams.lowerLimitValue"
          placeholder="请输入下限值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上限值" prop="upperLimitValue">
        <el-input
          v-model="queryParams.upperLimitValue"
          placeholder="请输入上限值"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备名称" prop="equipName">
        <el-input
          v-model="queryParams.equipName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="设备编号" prop="equipCode">
        <el-input
          v-model="queryParams.equipCode"
          placeholder="请输入设备编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参数描述" prop="description">
        <el-input
          v-model="queryParams.description"
          placeholder="请输入参数描述"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参数标识" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入参数标识"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参数名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入参数名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参数单位" prop="unit">
        <el-input
          v-model="queryParams.unit"
          placeholder="请输入参数单位"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工序ID" prop="workingProduceId">
        <el-input
          v-model="queryParams.workingProduceId"
          placeholder="请输入工序ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="参数分类" prop="classify">
        <el-input
          v-model="queryParams.classify"
          placeholder="请输入参数分类"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['mes:parameter:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['mes:parameter:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['mes:parameter:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['mes:parameter:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="62vh" v-loading="loading" :data="parameterList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="${comment}" align="center" prop="id" />
      <el-table-column label="	单位" align="center" prop="unitNew" />
      <el-table-column label="标准值" align="center" prop="standardValue" />
      <el-table-column label="下限值" align="center" prop="lowerLimitValue" />
      <el-table-column label="上限值" align="center" prop="upperLimitValue" />
      <el-table-column label="设备名称" align="center" prop="equipName" />
      <el-table-column label="设备编号" align="center" prop="equipCode" />
      <el-table-column label="参数描述" align="center" prop="description" />
      <el-table-column label="参数类型" align="center" prop="type" />
      <el-table-column label="参数标识" align="center" prop="code" />
      <el-table-column label="参数名称" align="center" prop="name" />
      <el-table-column label="参数单位" align="center" prop="unit" />
      <el-table-column label="工序ID" align="center" prop="workingProduceId" />
      <el-table-column label="参数分类" align="center" prop="classify" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['mes:parameter:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['mes:parameter:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改工序属性对话框 -->
          <el-drawer :title="title" :visible.sync="open" :size="'50%'" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" >
        <el-form-item label="	单位" prop="unitNew" style="width: 700px;">
          <el-input v-model="form.unitNew" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="标准值" prop="standardValue" style="width: 240px;">
          <el-input v-model="form.standardValue" placeholder="请输入标准值" />
        </el-form-item>
        <el-form-item label="下限值" prop="lowerLimitValue" style="width: 240px;">
          <el-input v-model="form.lowerLimitValue" placeholder="请输入下限值" />
        </el-form-item>
        <el-form-item label="上限值" prop="upperLimitValue" style="width: 240px;">
          <el-input v-model="form.upperLimitValue" placeholder="请输入上限值" />
        </el-form-item>
        <el-form-item label="设备名称" prop="equipName" style="width: 240px;">
          <el-input v-model="form.equipName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备编号" prop="equipCode" style="width: 240px;">
          <el-input v-model="form.equipCode" placeholder="请输入设备编号" />
        </el-form-item>
        <el-form-item label="参数描述" prop="description" style="width: 240px;">
          <el-input v-model="form.description" placeholder="请输入参数描述" />
        </el-form-item>
        <el-form-item label="参数标识" prop="code" style="width: 240px;">
          <el-input v-model="form.code" placeholder="请输入参数标识" />
        </el-form-item>
        <el-form-item label="参数名称" prop="name" style="width: 240px;">
          <el-input v-model="form.name" placeholder="请输入参数名称" />
        </el-form-item>
        <el-form-item label="参数单位" prop="unit" style="width: 240px;">
          <el-input v-model="form.unit" placeholder="请输入参数单位" />
        </el-form-item>
        <el-form-item label="工序ID" prop="workingProduceId" style="width: 240px;">
          <el-input v-model="form.workingProduceId" placeholder="请输入工序ID" />
        </el-form-item>
        <el-form-item label="参数分类" prop="classify" style="width: 240px;">
          <el-input v-model="form.classify" placeholder="请输入参数分类" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
      </el-form>
              <div class="demo-drawer__footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-drawer>
      </div>
  </div>
</template>

<script>
import { listParameter, getParameter, delParameter, addParameter, updateParameter } from "@/api/mes/parameter";

export default {
  name: "Parameter",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 工序属性表格数据
      parameterList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        unitNew: null,
        standardValue: null,
        lowerLimitValue: null,
        upperLimitValue: null,
        equipName: null,
        equipCode: null,
        description: null,
        type: null,
        code: null,
        name: null,
        unit: null,
        workingProduceId: null,
        classify: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        workingProduceId: [
          { required: true, message: "工序ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询工序属性列表 */
    getList() {
      this.loading = true;
      listParameter(this.queryParams).then(response => {
        this.parameterList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        unitNew: null,
        standardValue: null,
        lowerLimitValue: null,
        upperLimitValue: null,
        equipName: null,
        equipCode: null,
        description: null,
        type: null,
        code: null,
        name: null,
        unit: null,
        workingProduceId: null,
        classify: null,
        updateBy: null,
        updateTime: null,
        createBy: null,
        createTime: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加工序属性";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      getParameter(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工序属性";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateParameter(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addParameter(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除工序属性编号为"' + ids + '"的数据项？').then(function() {
        return delParameter(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('mes/parameter/export', {
        ...this.queryParams
      }, `工序属性_${new Date().toLocaleDateString()}.xlsx`)
    }
  }
};
</script>
