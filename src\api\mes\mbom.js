import request from '@/utils/request'

// 查询BOM管理列表
export function listMbom(query) {
  return request({
    url: '/mes/mbom/list',
    method: 'get',
    params: query
  })
}

// 查询BOM管理详细
export function getMbom(id) {
  return request({
    url: '/mes/mbom/' + id,
    method: 'get'
  })
}

// 新增BOM管理
export function addMbom(data) {
  return request({
    url: '/mes/mbom',
    method: 'post',
    data: data
  })
}

// 修改BOM管理
export function updateMbom(data) {
  return request({
    url: '/mes/mbom',
    method: 'put',
    data: data
  })
}

// 删除BOM管理
export function delMbom(id) {
  return request({
    url: '/mes/mbom/' + id,
    method: 'delete'
  })
}

// 查询BOM工单树形结构
export function getTreeList() {
  return request({
    url: '/mes/mbom/listTree' ,
    method: 'get'
  })
}
