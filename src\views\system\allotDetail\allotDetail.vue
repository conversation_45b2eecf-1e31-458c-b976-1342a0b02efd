<template>
  <div class="">
    <div class="">
      <el-form
        v-show="activeName == 'first'"
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        label-width="68px"
      >
        <el-form-item label="调拨单号" prop="allotNo">
          <el-input
            v-model="queryParams.allotNo"
            placeholder="请输入调拨单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="调拨状态" prop="allotState">
          <el-input
            v-model="queryParams.allotState"
            placeholder="请输入调拨状态"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
      <el-table
        height="62vh"
        v-loading="loading"
        :data="allotDetailList"
        @selection-change="handleSelectionChange"
        @row-click="sendDetailId"
      >
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="${comment}" align="center" prop="id" /> -->
        <el-table-column label="调拨单号" align="center" prop="allotNo" />
        <!-- <el-table-column label="调拨主表id" align="center" prop="allotId" /> -->
        <!-- <el-table-column label="物料id" align="center" prop="materialId" /> -->
        <el-table-column label="物料编码" align="center" prop="materialCode" />
        <el-table-column label="物料名称" align="center" prop="materialName" />
        <el-table-column label="规格型号" align="center" prop="specification" />
        <el-table-column label="单位" align="center" prop="materialUnit" />
        <el-table-column label="数量" align="center" prop="qty" />
        <el-table-column label="已调拨数量" align="center" prop="incomingQty" />
        <el-table-column label="批次" align="center" prop="batchNo" />
        <!-- <el-table-column label="调拨状态" align="center" prop="allotState" /> -->
        <el-table-column label="调拨状态" align="center" prop="allotState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.allot_state"
              :value="scope.row.allotState"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="组织" align="center" prop="comId" />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
  </div>
</template>

<script>
import {
  listAllotDetail,
  getAllotDetail,
  delAllotDetail,
  addAllotDetail,
  updateAllotDetail,
} from "@/api/system/allotDetail";

export default {
  props: ["allot_id", "activeName"],
  name: "componet_name",
  dicts: ["stock_in_type", "stock_in_state", "quality_state","allot_state"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsAllotBox: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 调拨单明细表格数据
      allotDetailList: [],
      // 调拨标签表格数据
      wmsAllotBoxList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        allotNo: null,
        allotId: this.allot_id,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        incomingQty: null,
        batchNo: null,
        allotState: null,
        comId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        allotNo: [
          { required: true, message: "调拨单号不能为空", trigger: "blur" },
        ],
        allotId: [
          { required: true, message: "调拨主表id不能为空", trigger: "blur" },
        ],
        materialId: [
          { required: true, message: "物料id不能为空", trigger: "blur" },
        ],
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        materialName: [
          { required: true, message: "物料名称不能为空", trigger: "blur" },
        ],
        qty: [{ required: true, message: "数量不能为空", trigger: "blur" }],
        delFlag: [
          { required: true, message: "删除标志不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getList();
    console.log("allot_id", this.allot_id);
  },
  methods: {
    /** 查询调拨单明细列表 */
    getList() {
      this.loading = true;
      listAllotDetail(this.queryParams).then((response) => {
        this.allotDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
        // 调用 box 组件的 getList 方法
        // if (this.$refs.allotBox) {
        //   console.log("this.$refs.allotBox", this.$refs.allotBox);
        //   this.$refs.allotBox.getList();
        // }
      });
    },
    sendDetailId(row, colom, env) {
      this.$emit("sendDetailId", row.id);
      console.log("row");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        allotNo: null,
        allotId: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
        qty: null,
        incomingQty: null,
        batchNo: null,
        allotState: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.wmsAllotBoxList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加调拨单明细";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getAllotDetail(id).then((response) => {
        this.form = response.data;
        this.wmsAllotBoxList = response.data.wmsAllotBoxList;
        this.open = true;
        this.title = "修改调拨单明细";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.wmsAllotBoxList = this.wmsAllotBoxList;
          if (this.form.id != null) {
            updateAllotDetail(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAllotDetail(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除调拨单明细编号为"' + ids + '"的数据项？')
        .then(function () {
          return delAllotDetail(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 调拨标签序号 */
    rowWmsAllotBoxIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 调拨标签添加按钮操作 */
    handleAddWmsAllotBox() {
      let obj = {};
      obj.allotNo = "";
      obj.allotId = "";
      obj.materialId = "";
      obj.materialCode = "";
      obj.materialName = "";
      obj.specification = "";
      obj.materialUnit = "";
      obj.qty = "";
      obj.boxNo = "";
      obj.qrCode = "";
      obj.warehouseId = "";
      obj.warehouseCode = "";
      obj.warehouseName = "";
      obj.areaId = "";
      obj.areaCode = "";
      obj.areaName = "";
      obj.locationId = "";
      obj.locationCode = "";
      obj.locationName = "";
      obj.batchNo = "";
      obj.isStaging = "";
      obj.supplierId = "";
      obj.supplierCode = "";
      obj.supplierName = "";
      obj.remark = "";
      obj.comId = "";
      this.wmsAllotBoxList.push(obj);
    },
    /** 调拨标签删除按钮操作 */
    handleDeleteWmsAllotBox() {
      if (this.checkedWmsAllotBox.length == 0) {
        this.$modal.msgError("请先选择要删除的调拨标签数据");
      } else {
        const wmsAllotBoxList = this.wmsAllotBoxList;
        const checkedWmsAllotBox = this.checkedWmsAllotBox;
        this.wmsAllotBoxList = wmsAllotBoxList.filter(function (item) {
          return checkedWmsAllotBox.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsAllotBoxSelectionChange(selection) {
      this.checkedWmsAllotBox = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/allotDetail/export",
        {
          ...this.queryParams,
        },
        `调拨单明细_${new Date().toLocaleDateString()}.xlsx`
      );
    },
  },
};
</script>
