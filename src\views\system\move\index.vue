<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="移库单号" prop="moveNo">
          <el-input
            v-model="queryParams.moveNo"
            placeholder="请输入移库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <!-- <el-form-item label="仓库id" prop="warehouseId">
        <el-input
          v-model="queryParams.warehouseId"
          placeholder="请输入仓库id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="仓库编码" prop="warehouseCode">
        <el-input
          v-model="queryParams.warehouseCode"
          placeholder="请输入仓库编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="仓库名称" prop="warehouseName">
        <el-input
          v-model="queryParams.warehouseName"
          placeholder="请输入仓库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="移库类型，字典move_type" prop="moveType">
        <el-select v-model="queryParams.moveType" placeholder="请选择移库类型，字典move_type" clearable>
          <el-option
            v-for="dict in dict.type.move_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
        <el-form-item label="移库状态" prop="moveStateArr">
          <el-select
            v-model="queryParams.moveStateArr"
            placeholder="请选择移库状态"
            clearable
            multiple
          >
            <el-option
              v-for="dict in dict.type.move_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="组织" prop="comId">
        <el-input
          v-model="queryParams.comId"
          placeholder="请输入组织"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:move:add']"
            >新增</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:move:edit']"
        >修改</el-button>
      </el-col> -->
        <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:move:remove']"
        >删除</el-button>
      </el-col> -->
        <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:move:export']"
        >导出</el-button>
      </el-col> -->
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table
        height="62vh"
        v-loading="loading"
        :data="moveList"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="index" width="50" align="center" />
        <el-table-column type="selection" width="55" align="center" />
        <!-- <el-table-column label="" align="center" prop="id" /> -->
        <el-table-column
          label="移库单号"
          align="center"
          prop="moveNo"
          :width="tableWidth(moveList.map((x) => x.moveNo))"
        >
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.moveNo"
              >
                <span class="ellipsis" style="display: inline-block">{{
                  scope.row.moveNo
                }}</span>
              </el-tooltip>
              <i
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.moveNo"
                v-clipboard:success="onCopy"
              ></i>
            </div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="仓库id" align="center" prop="warehouseId" /> -->
        <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
        <el-table-column
          label="仓库名称"
          width="90"
          align="center"
          prop="warehouseName"
        />
        <el-table-column label="移库类型" align="center" prop="moveType">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.move_type"
              :value="scope.row.moveType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="移库状态"
          width="80"
          align="center"
          prop="moveState"
        >
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.move_state"
              :value="scope.row.moveState"
            />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <!-- <el-table-column label="组织" align="center" prop="comId" /> -->
        <el-table-column label="创建者ID" align="center" prop="createBy" />
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="更新者ID" align="center" prop="updateBy" />
        <el-table-column
          label="更新时间"
          align="center"
          prop="updateTime"
          width="120"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.updateTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          min-width="180"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:move:edit']"
              >修改
            </el-button>
            <el-button
              size="mini"
              type="text"
              icon="el-icon-edit"
              @click="handleDatail(scope.row)"
              v-hasPermi="['system:move:edit']"
            >
              详情
            </el-button>
            <!-- 录入完成按钮：点击后将状态改为 "待入库" -->
            <el-button
              v-if="scope.row.moveState === 'CREATED'"
              size="mini"
              type="text"
              icon="el-icon-check"
              @click="handleComplete(scope.row)"
              v-hasPermi="['system:move:edit']"
              >录入完成</el-button
            >
            <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:move:remove']"
          >删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- <el-table height="62vh" v-loading="loading" :data="moveDetailList" >
      <el-table-column type="index" width="55" align="center" />
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="" align="center" prop="id" />
      <el-table-column label="移库单号" align="center" prop="moveNo" />
      <el-table-column label="主表id" align="center" prop="moveId" />
      <el-table-column label="仓库id" align="center" prop="warehouseId" />
      <el-table-column label="仓库编码" align="center" prop="warehouseCode" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="移库状态" align="center" prop="moveState">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.move_state" :value="scope.row.moveState"/>
        </template>
      </el-table-column>
      <el-table-column label="物料id" align="center" prop="materialId" />
      <el-table-column label="物料编码" align="center" prop="materialCode" />
      <el-table-column label="物料名称" align="center" prop="materialName" />
      <el-table-column label="单位" align="center" prop="materialUnit" />
      <el-table-column label="规格型号" align="center" prop="specification" />
      <el-table-column label="数量" align="center" prop="qty" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="组织" align="center" prop="comId" />
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParamsDetail.pageNum"
      :limit.sync="queryParamsDetail.pageSize"
      @pagination="getListDetail"
    /> -->
      <el-dialog
        title="选择仓库"
        :visible.sync="warehouseDialogVisible"
        width="50%"
      >
        <el-table
          :data="warehouseList"
          v-loading="warehouseLoading"
          @row-click="selectWarehouse"
          height="400"
          border
        >
          <el-table-column
            prop="warehouseCode"
            label="仓库编码"
            align="center"
          />
          <el-table-column
            prop="warehouseName"
            label="仓库名称"
            align="center"
          />
        </el-table>
        <div slot="footer" class="dialog-footer">
          <el-button @click="warehouseDialogVisible = false">取消</el-button>
        </div>
      </el-dialog>

      <!-- 明细添加物料对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="dialogVisible"
        width="50%"
        :before-close="handleClose"
      >
        <el-form :data="materialList" :inline="true" label-width="100px">
          <el-form-item label="物料编码" prop="materialCode">
            <el-input
              v-model="queryParamsMaterial.materialCode"
              placeholder="请输入物料编码"
              clearable
              @keyup.enter.native="handleQueryMaterial"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              icon="el-icon-search"
              size="mini"
              @click="handleQueryMaterial"
              >搜索</el-button
            >
            <el-button
              icon="el-icon-refresh"
              size="mini"
              @click="resetQueryMaterial"
              >重置</el-button
            >
          </el-form-item>
          <el-table
            ref="materialTable"
            :data="materialList"
            border
            stripe
            style="width: 100%"
            @selection-change="handleSelectionChangematerial"
          >
            <el-table-column type="selection" align="center" />
            <el-table-column
              label="物料编码"
              align="center"
              prop="materialCode"
            />
            <el-table-column
              label="物料名称"
              align="center"
              prop="materialName"
            />
            <el-table-column
              label="规格型号"
              align="center"
              prop="specification"
            />
            <el-table-column label="单位" align="center" prop="materialUnit" />

            <el-table-column label="数量" align="center" prop="qty" />
          </el-table>
        </el-form>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="loadListMaterials"
        />
        <span slot="footer" class="dialog-footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button type="primary" @click="handleAddMaterial">确 定</el-button>
        </span>
      </el-dialog>

      <!-- 详情对话框-->
      <el-drawer
        :title="detailTitle"
        :visible.sync="openDetail"
        :size="'75%'"
        append-to-body
      >
        <el-form
          ref="form"
          :model="form"
          size="small"
          :inline="true"
          label-width="100px"
        >
          <!-- <el-date-picker
              clearable
              v-model="form.stockOutDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="请选择出库日期"
            >
            </el-date-picker> -->
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="移库单信息" name="1">
              <el-form-item
                label="仓库编码"
                prop="warehouseCode"
                style="width: 240px"
              >
                <el-input
                  disabled
                  v-model="form.warehouseCode"
                  placeholder=""
                  @focus="openWarehouseDialog"
                />
              </el-form-item>
              <el-form-item
                label="仓库名称"
                prop="warehouseName"
                style="width: 240px"
              >
                <el-input
                  v-model="form.warehouseName"
                  placeholder=""
                  disabled
                />
              </el-form-item>
              <el-form-item
                label="移库类型"
                prop="moveType"
                style="width: 240px"
              >
                <el-select
                  disabled
                  v-model="form.moveType"
                  placeholder=""
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.move_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="移库单明细信息" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane
                  label="物料明细"
                  name="first"
                  :data="form.wmsMoveDetailList"
                >
                  <MoveDetail
                    :move_id="move_id"
                    :key="move_id"
                    :activeName="activeName"
                  />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <MoveDetail
                        @sendDetailId="receiveDetailData"
                        :move_id="move_id"
                        :key="move_id"
                        :activeName="activeName"
                        :tableData="form.wmsMoveDetailList"
                      />
                    </div>
                    <div style="width: 48%">
                      <MoveBox
                        :move_detail_id="move_detail_id"
                        :key="move_detail_id"
                      />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
      <!-- 添加或修改物料移库对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="移库单信息" name="1">
              <!-- <el-form-item label="移库单号" prop="moveNo" style="width: 240px;">
          <el-input v-model="form.moveNo" placeholder="请输入移库单号" />
        </el-form-item> -->
              <!-- <el-form-item label="仓库id" prop="warehouseId" style="width: 240px;">
          <el-input v-model="form.warehouseId" placeholder="请输入仓库id" />
        </el-form-item> -->
              <el-row class="mb8">
                <el-col :span="12">
                  <el-form-item
                    label="仓库编码"
                    prop="warehouseCode"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.warehouseCode"
                      placeholder="请输入仓库编码"
                      @focus="openWarehouseDialog"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="仓库名称"
                    prop="warehouseName"
                    style="width: 240px"
                  >
                    <el-input
                      v-model="form.warehouseName"
                      placeholder="请输入仓库名称"
                      disabled
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item
                label="移库类型"
                prop="moveType"
                style="width: 240px"
              >
                <el-select
                  v-model="form.moveType"
                  placeholder="请选择移库类型"
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.move_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="移库状态" prop="moveState" style="width: 240px;">
          <el-select v-model="form.moveState" placeholder="请选择移库状态" style="width: 240px;">
            <el-option
              v-for="dict in dict.type.move_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
              <!-- <el-form-item label="备注" prop="remark" style="width: 700px;">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="组织" prop="comId" style="width: 240px;">
          <el-input v-model="form.comId" placeholder="请输入组织" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag" style="width: 240px;">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item> -->
            </el-collapse-item>
            <el-collapse-item title="移库单明细信息" name="2">
              <el-row :gutter="10" class="mb8" style="margin-left: 5px">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAddWmsMoveDetail"
                    >添加</el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="handleDeleteWmsMoveDetail"
                    >删除</el-button
                  >
                </el-col>
              </el-row>

              <el-form
                ref="detailform"
                :model="{ wmsMoveDetailList }"
                :rules="detailrules"
              >
                <el-table
                  :data="wmsMoveDetailList"
                  :row-class-name="rowWmsMoveDetailIndex"
                  @selection-change="handleWmsMoveDetailSelectionChange"
                  ref="wmsMoveDetail"
                >
                  <el-table-column type="selection" width="50" align="center" />
                  <el-table-column
                    label="序号"
                    align="center"
                    prop="index"
                    width="50"
                  />
                  <!-- <el-table-column label="移库单号" prop="moveNo" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.moveNo" placeholder="请输入移库单号" disabled/>
            </template>
          </el-table-column> -->
                  <!-- <el-table-column label="仓库id" prop="warehouseId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.warehouseId" placeholder="请输入仓库id" disabled/>
            </template>
          </el-table-column> -->
                  <!-- <el-table-column label="仓库编码" prop="warehouseCode" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.warehouseCode" placeholder="请输入仓库编码" disabled/>
            </template>
          </el-table-column> -->
                  <!-- <el-table-column label="仓库名称" prop="warehouseName" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.warehouseName" placeholder="请输入仓库名称" disabled/>
            </template>
          </el-table-column> -->
                  <!-- <el-table-column label="移库状态" prop="moveState" width="150">
            <template slot-scope="scope">
              <el-select v-model="scope.row.moveState" placeholder="请选择移库状态">
                <el-option
                  v-for="dict in dict.type.move_state"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </template>
          </el-table-column> -->
                  <!-- <el-table-column label="物料id" prop="materialId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.materialId" placeholder="请输入物料id" />
            </template>
          </el-table-column> -->
                  <el-table-column
                    label="物料编码"
                    prop="materialCode"
                    width="160"
                  >
                    <!-- <template slot-scope="scope">
              <el-select
                  v-model="scope.row.materialCode"
                  placeholder="请选择物料编码"
                  clearable
                  style="width: 140px"
                   @change="handleMaterialChange(scope.row)"
                >
                  <el-option
                    v-for="material in materialList"
                    :key="material.id"
                    :label="material.materialCode"
                    :value="material.materialCode"
                  />
                </el-select>
            </template> -->
                  </el-table-column>
                  <el-table-column
                    label="物料名称"
                    prop="materialName"
                    width="150"
                  >
                    <!-- <template slot-scope="scope">
              <el-input v-model="scope.row.materialName" placeholder="请输入物料名称" disabled/>
            </template> -->
                  </el-table-column>
                  <el-table-column label="单位" prop="materialUnit" width="150">
                    <!-- <template slot-scope="scope">
              <el-input v-model="scope.row.materialUnit" placeholder="请输入单位" />
            </template> -->
                  </el-table-column>
                  <el-table-column
                    label="规格型号"
                    prop="specification"
                    width="150"
                  >
                    <!-- <template slot-scope="scope">
              <el-input v-model="scope.row.specification" placeholder="请输入规格型号" />
            </template> -->
                  </el-table-column>
                  <el-table-column label="数量" prop="qty" width="150">
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'wmsMoveDetailList.' + scope.$index + '.qty'"
                        style="display: inline-grid"
                        :rules="detailrules.qty"
                      >
                        <el-input
                          v-model.number="scope.row.qty"
                          type="number"
                          placeholder="请输入数量"
                        />
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="组织" prop="comId" width="150">
            <template slot-scope="scope">
              <el-input v-model="scope.row.comId" placeholder="请输入组织" />
            </template>
          </el-table-column> -->
                </el-table>
              </el-form>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div style="margin: 5px; display: flex; justify-content: end">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  listMove,
  getMove,
  delMove,
  addMove,
  updateMove,
  delMoveDetail,
} from "@/api/system/move";
import MoveDetail from "@/views/system/moveDetail/moveDetail.vue";
import MoveBox from "@/views/system/moveBox/moveBox.vue";
import { listMaterial } from "@/api/system/material";
import { listWarehouse } from "@/api/system/warehouse";
import { listMoveDetail, getMoveDetail } from "@/api/system/moveDetail";
import { listInventoryByWarehose } from "@/api/system/inventory";
export default {
  name: "Move",
  dicts: ["move_state", "move_type"],
  components: {
    MoveDetail,
    MoveBox,
  },
  data() {
    return {
      warehouseDialogVisible: false, // 仓库选择弹窗是否显示
      warehouseList: [], // 仓库列表数据
      warehouseLoading: false, // 仓库列表加载状态
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 删除移库单号
      delMove: [],
      // 子表选中数据
      checkedWmsMoveDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      activeNames: ["1", "2"],
      activeNamesInfo: ["1", "2"],
      detailTitle: "",
      openDetail: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 控制明细添加物料对话框
      dialogVisible: false,
      // 物料移库表格数据
      moveList: [],
      // 物料移库单明细表格数据
      moveDetailList: [],
      //获取物料列表
      materialList: [],
      // 选中的物料数据
      detailmaterialList: [],
      activeName: "first",
      move_id: "",
      move_detail_id: "",
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        moveNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        moveType: null,
        moveState: null, // 初始化为数组
        moveStateArr: [],
        comId: null,
      },
      // 查询参数
      queryParamsDetail: {
        pageNum: 1,
        pageSize: 10,
        moveNo: null,
        moveId: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        moveState: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        materialUnit: null,
        specification: null,
        qty: null,
        comId: null,
      },
      // 物料查询参数
      queryParamsMaterial: {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
      },
      wmsMoveDetailList: [],
      // 表单参数
      form: {
        //wmsMoveDetailList: [],
      },
      // 表单参数
      detailForm: {},
      // 表单校验
      rules: {
        warehouseCode: [
          { required: true, message: "仓库编码不能为空", trigger: "blur" },
        ],
        // warehouseName: [
        //   { required: true, message: "仓库名称不能为空", trigger: "blur" }
        // ],
        moveType: [
          { required: true, message: "移库类型不能为空", trigger: "blur" },
        ],
      },
      detailrules: {
        qty: [
          { required: true, message: "数量不能为空", trigger: "blur" },
          { type: "number", message: "必须为数字", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (value <= 0) {
                callback(new Error("数量必须大于0"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    // this.loadListMaterials();
    this.getListDetail();
    this.getList();
  },
  methods: {
    // 选择仓库
    selectWarehouse(row) {
      this.form.warehouseCode = row.warehouseCode; // 将仓库编码赋值到移库表单
      this.form.warehouseName = row.warehouseName; // 将仓库名称赋值到移库表单
      this.form.warehouseId = row.id; // 将仓库ID赋值到移库表单
      this.warehouseDialogVisible = false; // 关闭弹窗
      // 同时清空底下的暂存的仓库物料
      this.wmsMoveDetailList = [];
    },
    // 打开仓库选择弹窗
    openWarehouseDialog() {
      this.warehouseDialogVisible = true;
      this.loadWarehouseList();
    },
    // 加载仓库列表数据
    loadWarehouseList() {
      this.warehouseLoading = true;
      listWarehouse()
        .then((response) => {
          this.warehouseList = response.rows; // 假设接口返回的数据在 rows 中
          this.warehouseLoading = false;
        })
        .catch(() => {
          this.warehouseLoading = false;
        });
    },

    /** 查询物料移库列表 */
    getList() {
      this.loading = true;
      listMove(this.queryParams).then((response) => {
        // this.moveList = this.sortArrayByField(response.rows, "createTime");
        this.moveList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getlistMaterial() {
      this.loading = true;
      listMaterial(this.queryParamsMaterial).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        //console.log("this.materialList", this.materialList);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        moveNo: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        moveType: null,
        moveState: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
      };
      this.wmsMoveDetailList = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // 打印查询参数，确保 moveState 是数组
      console.log("查询参数movestate：", this.queryParams.move_state);
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.delMove = selection.map((item) => item.moveNo);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 从 dict 中获取“已创建”的值
      const createdState = this.dict.type.move_state.find(
        (item) => item.label === "已创建"
      );
      this.form.moveState = createdState ? createdState.value : null; // 设置默认状态
      this.open = true;
      this.title = "添加物料移库";
      this.wmsMoveDetailList = [];
    },
    onCopy() {
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getMove(id).then((response) => {
        this.form = response.data;
        this.wmsMoveDetailList = response.data.wmsMoveDetailList;
        this.open = true;
        this.title = "修改物料移库";
      });
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
        }
      });
      // 校验主表数据是否存在
      if (!this.form.warehouseCode || !this.form.moveType) {
        this.$modal.msgError("请新增至少一条移库信息");
        return; // 阻止提交
      }

      // 校验明细表数据是否存在
      if (this.wmsMoveDetailList.length === 0) {
        this.$modal.msgError("请至少新增一条明细数据");
        return; // 阻止提交
      }

      // 校验通过后，继续执行表单验证和提交逻辑
      this.$refs["form"]
        .validate()
        .then((formValid) => {
          this.$refs["detailform"]
            .validate()
            .then((detailValid) => {
              if (formValid && detailValid) {
                // 两个表单都校验通过
                this.form.wmsMoveDetailList = this.wmsMoveDetailList;

                if (this.form.id != null) {
                  updateMove(this.form).then((response) => {
                    this.$modal.msgSuccess("修改成功");
                    this.open = false;
                    this.getList();
                  });
                } else {
                  addMove(this.form).then((response) => {
                    this.$modal.msgSuccess("新增成功");
                    this.open = false;
                    this.getList();
                  });
                }
              }
            })
            .catch((detailError) => {
              // detailform 校验失败
              console.error("明细表单校验失败:", detailError);
            });
        })
        .catch((formError) => {
          // 主表单校验失败
          console.error("主表单校验失败:", formError);
        });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      const delMove = row.moveNo || this.delMove;
      this.$modal
        .confirm('是否确认删除物料移库编号为"' + delMove + '"的数据项？')
        .then(function () {
          return delMove(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 移库单明细序号 */
    rowWmsMoveDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    handleAddWmsMoveDetail() {
      this.dialogVisible = true;
      this.title = "新增移库单明细";
      // 清空选中的物料数据
      this.detailmaterialList = [];
      this.materialList = [];
      this.loadListMaterials();
      // this.loadListMaterials();
      console.log("this.wmsMoveDetailList", this.wmsMoveDetailList);
      this.ids = [];

      // 清除表格选中状态
      this.$nextTick(() => {
        const tableRef = this.$refs.materialTable; // 获取表格的 ref
        if (tableRef) {
          tableRef.clearSelection(); // 清除选中状态
        }
      });
      // let obj = {
      // moveNo: this.form.moveNo,
      // warehouseId: this.form.warehouseId,
      // warehouseCode: this.form.warehouseCode,
      // warehouseName: this.form.warehouseName,
      // moveState: this.form.moveState,
      // materialId: "",
      // materialCode: "",
      // materialName: "",
      // materialUnit: "",
      // specification: "",
      // qty: "",
      // remark: "",
      // comId: "",
      //isNew: true, // 标记为新增数据
      //willDel: false, // 初始化 willDel 标志为 false
    },

    handleClose() {
      this.dialogVisible = false;
      // 清除表格选中状态
      this.$nextTick(() => {
        const tableRef = this.$refs.materialTable; // 获取表格的 ref
        if (tableRef) {
          tableRef.clearSelection(); // 清除选中状态
        }
        this.loading = false; // 重置加载状态
      });

      // 清空选中的物料数据
      this.detailmaterialList = [];
      this.ids = [];
      this.resetQueryMaterial();
    },

    // 录入完成按钮操作
    handleComplete(row) {
      // 确认操作
      const ids = row.moveNo || this.ids;
      this.$modal
        .confirm('"是否确认将移库单编号为' + ids + '"的状态改为 "待移库"？"')
        .then(() => {
          // 从 dict 中获取“待移库”的值
          const createdState = this.dict.type.move_state.find(
            (item) => item.label === "待移库"
          );
          row.moveState = createdState ? createdState.value : null; // 设置默认状态
          // 更新明细表状态
          this.moveDetailList.forEach((detail) => {
            detail.moveState = createdState ? createdState.value : null; // 设置默认状态
          });
          // 更新状态为 "待入库"
          //row.moveState = this.dict.type.move_state.find(item => item.label === "待移库");
          console.log("更新后的状态：", row.moveState);
          // 调用后端接口更新状态
          console.log("row--------》：", row);
          row.wmsMoveDetailList = this.form.wmsMoveDetailList;
          updateMove(row).then(() => {
            this.$modal.msgSuccess("状态已更新为待移库");
            this.getList(); // 刷新列表
          });
        })
        .catch(() => {
          // 用户取消操作
        });
    },
    /** 移库单明细删除按钮操作 */
    handleDeleteWmsMoveDetail() {
      if (this.checkedWmsMoveDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的移库单明细数据");
      } else {
        const wmsMoveDetailList = this.wmsMoveDetailList;
        const checkedWmsMoveDetail = this.checkedWmsMoveDetail;
        this.wmsMoveDetailList = wmsMoveDetailList.filter(function (item) {
          return checkedWmsMoveDetail.indexOf(item.index) == -1;
        });
      }
    },
    /** 复选框选中数据 */
    handleWmsMoveDetailSelectionChange(selection) {
      this.checkedWmsMoveDetail = selection.map((item) => item.index);
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/move/export",
        {
          ...this.queryParams,
        },
        `物料移库_${new Date().toLocaleDateString()}.xlsx`
      );
    },

    // 自定义校验规则：检查是否为数字
    validateNumber(rule, value, callback) {
      if (value === null || value === undefined || value === "") {
        callback(); // 允许为空
      } else if (!/^\d+(\.\d+)?$/.test(value)) {
        callback(new Error("请输入有效数字")); // 非数字时提示错误
      } else {
        callback(); // 校验通过
      }
    },
    handleSelectionChangematerial(selection) {
      console.log("当前选中的数据：", selection); // 打印选中的数据
      console.log("当前materiallist：", this.materialList); // 打印选中的数据长度
      this.ids = selection.map((item) => item.id);
      console.log("选中的ID列表：", this.ids);
      // 获取选中项的ID数组

      // 根据ID筛选需要提交的数据
      this.detailmaterialList = this.materialList.filter((item) =>
        this.ids.includes(item.id)
      );
      console.log("筛选后的提交数据：", this.detailmaterialList);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleAddMaterial() {
      console.log("确定的时候this.detailmaterialList", this.detailmaterialList);
      if (this.detailmaterialList.length === 0) {
        this.$modal.msgError("请至少选择一条物料数据");
        return; // 阻止提交
      }
      for (let obj of this.detailmaterialList) {
        obj.materialId = obj.id;
        console.log("obj", obj);
        const exists = this.wmsMoveDetailList.some(
          (item) => item.materialId === obj.materialId
        );
        if (exists) {
          this.$message.warning(`物料: ${obj.materialName}已存在！`);
          break; // 跳出整个循环
        }
        this.wmsMoveDetailList.push(obj);
      }
      this.dialogVisible = false;
    },
    /** 删除按钮操作 */
    // handleDeleteWmsMoveDetail(row) {
    //   const ids = row.id || this.ids;
    //   this.$modal.confirm('是否确认删除移库单明细编号为"' + ids + '"的数据项？').then(function() {
    //     return delMoveDetail(ids);
    //   }).then(() => {
    //     this.getList();
    //     this.$modal.msgSuccess("删除成功");
    //   }).catch(() => {});
    // },
    /** 修改按钮操作 */
    handleUpdateWmsMoveDetail(row) {
      this.reset();
      const id = row.id || this.ids;
      getMoveDetail(id).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改移库单明细";
      });
    },
    //   // 加载物料列表
    // loadMaterialList() {
    //   listMaterial().then(response => {
    //     this.materialList = response.rows.map((item) => ({

    //     //materialId: item.id,
    //     materialCode: item.materialCode,
    //     materialName: item.materialName,
    //     specification: item.specification, // 映射规格型号
    //     materialUnit: item.materialUnit,   // 映射单位
    // }));
    // console.log("this.materialList", this.materialList);
    //   }).catch(() => {
    //     this.loading = false;
    //   });
    // },
    loadListMaterials() {
      // 根据仓库ID加载物料列表
      const params = {
        warehouseId: this.form.warehouseId,
        warehouseCode: this.form.warehouseCode,
        warehouseName: this.form.warehouseName,
      };
      console.log("this.queryParams", this.queryParams);
      console.log("this.form", this.form);
      //
      listInventoryByWarehose(params).then((response) => {
        this.materialList = response.rows;
        this.total = response.total;
        console.log("this.materialList", this.materialList);
      });
    },
    // 监听物料编码选择变化
    handleMaterialChange(row) {
      // 根据物料编码查找对应的物料信息
      const selectedMaterial = this.materialList.find(
        (material) => material.materialCode === row.materialCode
      );
      if (selectedMaterial) {
        // 更新物料 ID 和物料名称
        row.materialId = selectedMaterial.materialId;
        row.materialName = selectedMaterial.materialName;
      } else {
        // 如果未找到对应的物料，清空相关字段
        row.materialId = null;
        row.materialName = null;
      }
    },
    // 主表行点击事件
    handleRowClick(row) {
      // 更新查询参数，设置主表 id
      this.queryParamsDetail.moveId = row.id;

      // 调用获取明细数据的方法
      this.getListDetail();
    },
    /** 查询移库单明细列表 */
    getListDetail() {
      this.loading = true;
      listMoveDetail(this.queryParamsDetail).then((response) => {
        console.log("接口返回数据:", response); // 打印接口返回的完整数据
        this.moveDetailList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    receiveDetailData(data) {
      console.log("看看data", data);
      this.allot_detail_id = data;
    },
    /* 查看详情 */
    handleDatail(row) {
      console.log("看看", row);

      this.reset();
      this.activeName = "first";
      this.move_id = row.id;
      const id = row.id || this.ids;
      this.openDetail = true;
      getMove(id).then((response) => {
        this.form = response.data;
        this.form.wmsMoveDetailList = response.data.wmsMoveDetailList;
        this.detailTitle = "物料移库详情";
      });
    },
    /** 查询物料列表 */
    handleQueryMaterial() {
      this.queryParamsMaterial.pageNum = 1;
      this.getlistMaterial();
    },
    /** 重置物料查询按钮操作 */
    resetQueryMaterial() {
      this.resetForm("queryFormMaterial");
      // 重置查询参数
      this.queryParamsMaterial = {
        pageNum: 1,
        pageSize: 10,
        materialCode: null,
        materialName: null,
        specification: null,
        materialUnit: null,
      };

      // 清空选中的物料数据
      this.detailmaterialList = [];

      // 清空表格数据
      this.materialList = [];

      this.handleQueryMaterial();
    },
    /**标签点击 */
    handleClick() {
      this.move_detail_id = null;
    },
  },
};
</script>
