<template>
  <div class="app-container">
    <div class="app-container-div">
      <el-form
        :model="queryParams"
        ref="queryForm"
        size="small"
        :inline="true"
        v-show="showSearch"
        label-width="68px"
      >
        <el-form-item label="出库单号" prop="stockOutNo">
          <el-input
            v-model="queryParams.stockOutNo"
            placeholder="请输入出库单号"
            clearable
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item label="出库时间" prop="stockOutDate">
          <el-date-picker
            clearable
            v-model="queryParams.stockOutDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出库时间"
          ></el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="出库类型" prop="stockOutType">
          <el-select
            disabled
            v-model="queryParams.stockOutType"
            placeholder="请选择出库类型"
            clearable
          >
            <el-option
              v-for="dict in dict.type.stock_out_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>-->
        <el-form-item label="出库状态" prop="stockOutState">
          <el-select
            v-model="queryParams.stockOutState"
            placeholder="请选择出库状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.stock_out_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="质检状态" prop="qualityState">
          <el-select
            v-model="queryParams.qualityState"
            placeholder="请选择质检状态"
            clearable
          >
            <el-option
              v-for="dict in dict.type.quality_state"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>-->

        <el-form-item>
          <el-button
            type="primary"
            icon="el-icon-search"
            size="mini"
            @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="el-icon-plus"
            size="mini"
            @click="handleAdd"
            v-hasPermi="['system:stockOut:add']"
            >新增</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="el-icon-edit"
            size="mini"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:stockOut:edit']"
            >修改</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="el-icon-delete"
            size="mini"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:stockOut:remove']"
            >删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="el-icon-download"
            size="mini"
            @click="handleExport"
            v-hasPermi="['system:stockOut:export']"
            >导出</el-button
          >
        </el-col>-->
        <right-toolbar
          :showSearch.sync="showSearch"
          @queryTable="getList"
        ></right-toolbar>
      </el-row>

      <el-table height="62vh" v-loading="loading" :data="stockOutList">
        <!-- <el-table-column type="selection" width="55" align="center" >
        <template #default="{ row, $index }">
            <div v-if="columnCheckedId == row.id || checkedList[$index]">
              <el-checkbox v-model="checkedList[$index]" @change="cellCheckbox(row, $index)"></el-checkbox>
            </div>
            <span v-else>{{ $index + 1 }}</span>
          </template>
        </el-table-column> -->

        <el-table-column type="index" width="55" align="center" label="序号" />
        <el-table-column label="出库单号" align="center" prop="stockOutNo" :width="tableWidth(stockOutList.map(x => x.stockOutNo))">
          <template slot-scope="scope">
            <div class="inputInfo copy_icon">
              <el-tooltip
                placement="top"
                effect="dark"
                :content="scope.row.stockOutNo"
              >
                <span class="ellipsis" style="display: inline-block">
                  {{ scope.row.stockOutNo }}</span
                >
              </el-tooltip>
              <i
                style="margin-left: 10px; cursor: pointer"
                class="el-icon-document-copy"
                v-clipboard:copy="scope.row.stockOutNo"
                v-clipboard:success="onCopy"
              >
              </i>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="业务类型" align="center" prop="businessType">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.business_type"
              :value="scope.row.businessType"
            />
          </template>
        </el-table-column>

        <el-table-column label="出库类型" align="center" prop="stockOutType">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.stock_out_type"
              :value="scope.row.stockOutType"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="出库时间"
          align="center"
          prop="stockOutDate"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.stockOutDate, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column label="出库状态" align="center" prop="stockOutState">
          <template slot-scope="scope">
            <dict-tag
              :options="dict.type.stock_out_state"
              :value="scope.row.stockOutState"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          prop="remark"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建人"
          align="center"
          prop="createBy"
          show-overflow-tooltip
        />

        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="260"
          fixed="right"
        >
          <template slot-scope="scope">
            <div>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit-outline"
                @click="handleUpdate(scope.row)"
                v-show="scope.row.stockOutState == 'TO_BE_CREATED'"
                v-hasPermi="['system:stockOut:edit']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-check"
                @click="handlecomplete(scope.row)"
                v-show="scope.row.stockOutState == 'TO_BE_CREATED'"
                v-hasPermi="['system:stockOut:edit']"
                >录入完成</el-button
              >
              <!-- <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-show="scope.row.stockOutState == 'TO_BE_CREATED'"
              v-hasPermi="['system:stockOut:remove']"
            >
              删除
            </el-button>-->
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete-solid"
                @click="handlecancellation(scope.row)"
                v-show="
                  scope.row.stockOutState == 'TO_BE_CREATED' ||
                  scope.row.stockOutState == 'STOCK_OUT_PENDING'
                "
                v-hasPermi="['system:stockOut:remove']"
                >作废</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-s-order"
                @click="handleDatail(scope.row)"
                v-hasPermi="['system:stockOut:edit']"
                >详情</el-button
              >
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />

      <!-- 添加或修改出库单主对话框 -->
      <el-drawer
        :title="title"
        :visible.sync="open"
        :size="'50%'"
        append-to-body
      >
        <el-form ref="form" :model="form" :rules="rules">
          <el-collapse v-model="activeNames">
            <el-collapse-item title="其他出库单信息" name="1">
              <!-- 第一行 -->
              <el-row :gutter="10" class="mb8">
                <el-col :span="8">
                  <el-form-item disabled label="出库类型" prop="stockOutType">
                    <el-select
                      disabled
                      v-model="form.stockOutType"
                      placeholder="请选择出库类型"
                    >
                      <el-option
                        v-for="dict in dict.type.stock_out_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="出库时间" prop="stockOutDate">
                    <el-date-picker
                      clearable
                      v-model="form.stockOutDate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      placeholder="请选择出库时间"
                    ></el-date-picker>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="单据类型" prop="businessType">
                    <el-select
                      v-model="form.businessType"
                      placeholder="请选择单据类型"
                      clearable
                    >
                      <el-option
                        v-for="dict in dict.type.business_type"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="10" class="mb8">
                <el-col :span="8">
                  <el-form-item label="库存方向" prop="inventoryDirectionother">
                    <el-select
                      label="库存方向"
                      placeholder="请选择库存方向"
                      v-model="form.inventoryDirectionother"
                      clearable
                    >
                      <el-option
                        v-for="dict in dict.type.inventory_direction"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="24" class="mb8">
                <el-col :span="24">
                  <el-form-item label="备注" prop="remark">
                    <el-input
                      v-model="form.remark"
                      type="textarea"
                      placeholder="请输入内容"
                      maxlength="300"
                      show-word-limit
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-collapse-item>
            <el-collapse-item title="出库单明细信息" name="2">
              <!-- </el-form> -->

              <el-row :gutter="10" class="mb8" style="margin-left: 5px">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="importFuction"
                    >添加</el-button
                  >
                </el-col>

                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    @click="handleDeleteWmsStockOutDetail"
                    >删除</el-button
                  >
                </el-col>
              </el-row>

              <el-table
                :data="form.wmsStockOutDetailList"
                :row-class-name="rowWmsStockOutDetailIndex"
                @selection-change="handleSelectionChange"
                ref="wmsStockOutDetail"
                @cell-mouse-enter="cellEnter"
                @cell-mouse-leave="cellLeave"
              >
                <el-table-column type="selection" width="55" align="center">
                  <template #default="{ row, $index }">
                    <div
                      v-if="columnCheckedId == row.id || checkedList[$index]"
                    >
                      <el-checkbox
                        v-model="checkedList[$index]"
                        @change="cellCheckbox(row, $index)"
                      ></el-checkbox>
                    </div>
                    <span v-else>{{ $index + 1 }}</span>
                  </template>
                </el-table-column>

                <!-- <el-table-column type="selection" width="50" align="center" />
                <el-table-column label="序号" align="center" prop="index" width="50" /> -->
                <el-table-column
                  label="物料编码"
                  prop="materialCode"
                  width="150"
                ></el-table-column>

                <el-table-column
                  label="物料名称"
                  prop="materialName"
                  width="150"
                ></el-table-column>
                <el-table-column
                  label="规格型号"
                  prop="specification"
                  width="150"
                ></el-table-column>
                <el-table-column
                  label="单位"
                  prop="materialUnit"
                  width="150"
                ></el-table-column>
                <el-table-column label="数量">
                  <template slot-scope="scope">
                    <el-form-item
                      :prop="'wmsStockOutDetailList.' + scope.$index + '.qty'"
                      :rules="rules.qty"
                      class="qty-form-item"
                    >
                      <el-input
                        v-model="scope.row.qty"
                        placeholder="请输入数量"
                        @input="handleQtyInput(scope.row)"
                      ></el-input>
                    </el-form-item>
                    <!-- 错误提示 - 完整显示在输入框下方 -->
                    <div
                      v-show="scope.row.qtyError"
                      class="error-tip"
                      style="margin-top: 5px"
                    >
                      {{ scope.row.qtyError }}
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-form>
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-drawer>
      <!-- 详情对话框 -->
      <el-drawer
        :title="detailTitle"
        :visible.sync="openDetail"
        :size="'60%'"
        append-to-body
      >
        <el-form ref="form" size="small" :inline="true" label-width="100px">
          <el-collapse v-model="activeNamesInfo">
            <el-collapse-item title="其他出库单信息" name="1">
              <el-form-item label="出库单号" prop="stockOutNo">
                <el-input disabled v-model="form.stockOutNo" placeholder />
              </el-form-item>

              <el-form-item label="出库时间" prop="stockOutDate">
                <el-input disabled v-model="form.stockOutDate" placeholder />
              </el-form-item>

              <el-form-item label="单据类型" prop="businessType">
                <el-select
                  v-model="form.businessType"
                  placeholder="请选择单据类型"
                  clearable
                  disabled
                >
                  <el-option
                    v-for="dict in dict.type.business_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  ></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="库存方向" prop="inventoryDirectionother">
                <el-select
                  label="库存方向"
                  placeholder="请选择库存方向"
                  v-model="form.inventoryDirectionother"
                  clearable
                  disabled
                  style="width: 200px"
                >
                  <el-option
                    v-for="dict in dict.type.inventory_direction"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-collapse-item>
            <el-collapse-item title="出库单明细信息" name="2">
              <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane
                  label="出库明细"
                  name="first"
                  :data="form.wmsStockOutDetailList"
                >
                  <Outdetail
                    :stock_out_id="stock_out_id"
                    :key="stock_out_id"
                    :activeName="activeName"
                  />
                </el-tab-pane>
                <el-tab-pane label="标签明细" name="second">
                  <div style="display: flex; justify-content: space-evenly">
                    <div style="width: 48%">
                      <Outdetail
                        @sendDetailId="receiveDetailData"
                        :stock_out_id="stock_out_id"
                        :key="stock_out_id"
                        :activeName="activeName"
                        :tableData="form.wmsStockOutDetailList"
                      />
                    </div>
                    <div style="width: 48%">
                      <OutBox
                        :stock_detail_id="stock_detail_id"
                        :key="stock_detail_id"
                      />
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-drawer>
      <!-- 采购单抽屉 -->
      <el-drawer
        :title="purchaseTitle"
        :visible.sync="purchaseDrawerVisible"
        :size="'60%'"
        append-to-body
        @close="handleDrawerClose"
      >
        <!-- 使用栅格系统实现左右分栏 -->
        <!-- <el-row :gutter="24" style="height: 100%"> -->
        <!-- 左侧表单 -->
        <!-- <el-col :span="24"> -->
        <!-- <el-form
              ref="leftForm"
              :model="purchaseQueryParams"
              size="small"
              label-width="68px"
              class="left-form"
        >-->
        <el-form
          :model="purchaseQueryParams"
          ref="queryForm"
          size="small"
          :rules="rules"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-row :gutter="20">
            <el-form-item label="物料编码" label-width="100px">
              <el-input
                v-model="purchaseQueryParams.materialCode"
                placeholder="请输入物料编码"
                style="width: 150px"
                @keyup.enter.native="handlePurchaseQuery"
              />
            </el-form-item>
            <el-form-item label="物料名称" label-width="100px">
              <el-input
                v-model="purchaseQueryParams.materialName"
                placeholder="请输入物料名称"
                style="width: 150px"
                @keyup.enter.native="handlePurchaseQuery"
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                icon="el-icon-search"
                size="mini"
                @click="handlePurchaseQuery"
                >搜索</el-button
              >
              <el-button
                icon="el-icon-refresh"
                size="mini"
                @click="resetPurchaseQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-row>

          <el-table
            :data="purchaseList"
            highlight-current-row
            style="cursor: pointer"
            v-loading="purchaseLoading"
            height="60vh"
            @selection-change="handleSelectionDetail"
            @select-all="onSelectAll"
            @row-click="rowClick"
            ref="leftMultipleTable"
          >
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column type="index" width="55" align="center" />

            <el-table-column
              label="物料编码"
              prop="materialCode"
            ></el-table-column>
            <el-table-column
              label="物料名称"
              prop="materialName"
            ></el-table-column>
            <el-table-column
              label="规格型号"
              prop="specification"
            ></el-table-column>
            <el-table-column label="物料仓储数量" prop="qty"></el-table-column>
            <el-table-column label="单位" prop="materialUnit"></el-table-column>
          </el-table>
          <pagination
            v-show="purchasetotal > 0"
            :total="purchasetotal"
            :page.sync="purchaseQueryParams.pageNum"
            :limit.sync="purchaseQueryParams.pageSize"
            @pagination="loadPurchase"
          />
        </el-form>
        <!-- </el-col> -->

        <!-- 右侧表单 -->
        <!-- <el-col :span="12">
            <el-form
              ref="rightForm"
              :model="purchaseQueryDetailParams"
              size="small"
              label-width="100px"
              class="right-form"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="8">
                  <el-form-item label="物料编号">
                    <el-input v-model="purchaseQueryDetailParams.partCode" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="物料名称">
                    <el-input v-model="purchaseQueryDetailParams.partName" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button
                      type="primary"
                      icon="el-icon-search"
                      size="mini"
                      @click="handlePurchaseDetailQuery"
                      >搜索</el-button
                    >
                    <el-button
                      icon="el-icon-refresh"
                      size="mini"
                      @click="resetPurchaseDetailQuery"
                      >重置</el-button
                    >
                  </el-form-item></el-col
                >
              </el-row>
              <el-table
                :data="purchaseDetailList"
                @selection-change="handleSelectionDetail"
                ref="rightMultipleTable"
              >
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="物料编号" prop="partCode">
                </el-table-column>
                <el-table-column label="物料名称" prop="partName">
                </el-table-column>
              </el-table>
              <pagination
                v-show="purchaseDetailtotal > 0"
                :total="purchaseDetailtotal"
                :page.sync="purchaseQueryDetailParams.pageNum"
                :limit.sync="purchaseQueryDetailParams.pageSize"
                @pagination="loadPurchaseDetail"
              />
            </el-form>
        </el-col>-->
        <!-- </el-row> -->
        <div class="demo-drawer__footer">
          <el-button type="primary" @click="handleAddPurchaseDetail"
            >保 存</el-button
          >
          <el-button @click="handleClose">取 消</el-button>
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import {
  listStockOut,
  getStockOut,
  delStockOut,
  addStockOut,
  updateStockOut,
} from "@/api/system/stockOut";

import { listPurchase_detail } from "@/api/system/purchase_detail";
import {
  listPurchase,
  getPurchase,
  delPurchase,
  addPurchase,
  updatePurchase,
} from "@/api/system/purchase";
import {
  listInventory,
  getInventory,
  delInventory,
  addInventory,
  updateInventory,
  listInventoryGroup,
} from "@/api/system/inventory";
import Outdetail from "@/views/stockOut/stockOutDetail/outdetail.vue";
import OutBox from "@/views/stockOut/stockOutBox/OutBox.vue";
export default {
  name: "StockOut",
  dicts: [
    "quality_state",
    "stock_out_state",
    "stock_out_type",
    "return_method",
    "business_type",
    "inventory_direction",
  ],
  components: {
    Outdetail,
    OutBox, // 注册子组件
  },
  data() {
    return {
      isMenuOpen: false,
      columnCheckedId: "",
      multipleSelection: [], //全选
      checkedList: [], //table多选选中数据
      isAllSelected: false, // 全选是否选中
      todo_total: 9000,
      todo_pageNum: 1,
      todo_pageSize: 20,
      purchaseInputValue: "",
      isPurchaseOrderDisabled: false, // 控制采购订单框禁用状态
      activeNames: ["1", "2"],
      //详情
      activeNamesInfo: ["1", "2"],
      // 定义set用于存储push到数组里的id，方便新增去重
      existingIds: new Set(),
      //选中的明细
      selectedPurchaseDetailList: [],
      // 明细采购单明细
      purchaseDetailList: [],
      purchaseDetailtotal: 0,
      purchaseDetailLoading: false,
      // 选中的采购单表单数据
      selectedRows: [], // 确保初始化
      selectedIds: [],
      // 采购单表单数据
      purchaseList: [],
      purchasetotal: 0,
      purchaseLoading: false,
      // 采购单抽屉弹窗
      purchaseTitle: "",
      // 控制采购单抽屉
      purchaseDrawerVisible: false,

      isCompleted: false,
      detailTitle: "",
      openDetail: false,
      activeName: "first",
      stock_out_id: "",
      stock_detail_id: "",
      // labelPosition: "top",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 子表选中数据
      checkedWmsStockOutDetail: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 出库单主表格数据
      stockOutList: [],
      // // 出库单明细表格数据
      // wmsStockOutDetailList: [{ qty: null }],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        stockOutNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockOutType: "OTHER_OUT",
        stockOutDate: null,
        stockOutState: null,
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockOutOntherType: null,
        comId: null,
      },
      purchaseQueryParams: {
        pageNum: 1,
        pageSize: 10,
        supplierCode: "",
        supplierName: "",
        purchaseNo: "",
        purchaseNo: "",
      },
      purchaseQueryDetailParams: {
        pageNum: 1,
        pageSize: 10,
        partCode: "",
        partName: "",
      },
      // 表单参数
      form: {
        purchaseNo: "",
        purchaseNo: "",
        supplierCode: "",
        supplierName: "",
        wmsStockOutDetailList: [],
      },
      // 表单校验
      rules: {
        materialCode: [
          { required: true, message: "物料编码不能为空", trigger: "blur" },
        ],
        businessType: [
          { required: true, message: "单据类型不能为空", trigger: "change" },
        ],
        inventoryDirectionother: [
          { required: true, message: "库存方向不能为空", trigger: "change" },
        ],
        // stockOutDate: [
        //   { required: true, message: "出库时间不能为空", trigger: "change" },
        // ],
        // returnMethod: [
        //   { required: true, message: "退货方式不能为空", trigger: "change" },
        // ],
        // stockOutNo: [
        //   { required: true, message: "出库单号不能为空", trigger: "blur" },
        // ],
        //   stockOutType: [
        //     {
        //       required: true,
        //       message: "出库类型不能为空",
        //       trigger: "change",
        //     },
        //   ],
        //   stockOutState: [
        //     {
        //       required: true,
        //       message: "出库状态不能为空",
        //       trigger: "change",
        //     },
        //   ],
        //   purchaseNo: [
        //     { required: true, message: "采购单号不能为空", trigger: "blur" },
        //   ],
        //  qty: [
        //     { required: true, message: "数量不能为空", trigger: "blur" },
        //     {
        //       validator: (rule, value, callback) => {
        //         if (value === null || value === undefined || value === '') {
        //           callback();
        //         } else if (!/^[1-9]\d*$/.test(value)) {
        //           callback(new Error("请输入大于0的正整数"));
        //         } else {
        //           callback();
        //         }
        //       },
        //       trigger: ["blur", "change"]
        //     },
        //   ],
      },
    };
  },
  created() {
    this.getList();

    // this.loadPurchaseDetail();
  },
  methods: {
    toggleMenu() {
      this.isMenuOpen = !this.isMenuOpen;
    },
    handleAction(action) {
      console.log("执行操作:", action);
      this.isMenuOpen = false;
      // 这里可以触发相应的事件或调用方法
    },
    /**
     * 待办任务区域逻辑
     */
    // 获取数据
    getTodoListData() {},
    // 全选
    handleSelectionChange(val) {
      this.multipleSelection = val;
      this.checkedWmsStockOutDetail = val.map((item) => item.index);
      console.log(this.checkedWmsStockOutDetail, "77777777777777");
      // 全选
      this.isAllSelected =
        val.length === this.form.wmsStockOutDetailList.length;

      if (this.isAllSelected) {
        this.checkedList = this.form.wmsStockOutDetailList.map(
          (item, index) => {
            return true;
          }
        );
      } else {
        this.checkedList = this.form.wmsStockOutDetailList.map(
          (item, index) => {
            const isSelected = val.some((selected) => selected.id === item.id);
            return isSelected;
          }
        );
      }
    },
    //移入当前行
    cellEnter(row) {
      this.columnCheckedId = row.id;
    },

    // 移出当前行
    cellLeave(row) {
      this.columnCheckedId = "";
    },

    // 选中与否塞数据--反选
    cellCheckbox(row, index) {
      if (this.checkedList[index]) {
        this.$refs.wmsStockOutDetail.toggleRowSelection(row, true);
      } else {
        this.$refs.wmsStockOutDetail.toggleRowSelection(row, false);
      }
    },

    // 数量输入校验
    handleQtyInput(row) {
      // 清除之前的错误状态
      row.qtyError = null;
      // 空值校验
      if (!row.qty || row.qty.trim() === "") {
        row.qtyError = "数量不能为空";
        return;
      }
      // 仅允许输入数字
      row.qty = row.qty.replace(/[^\d]/g, "");
      // 正则校验：大于0的整数
      const reg = /^[1-9]\d*$/;
      if (!reg.test(row.qty)) {
        row.qtyError = "数量必须为大于0的整数";
        return;
      }

      // 移除前导零
      if (row.qty.startsWith("0")) {
        row.qty = row.qty.replace(/^0+/, "") || "";
      }

      // 自动触发验证更新
      this.$nextTick(() => {
        this.$refs.form.validateField(
          `wmsStockOutDetailList.${this.form.wmsStockOutDetailList.indexOf(
            row
          )}.qty`
        );
      });
    },
    /** 查询出库单主列表 */
    getList() {
      this.loading = true;
      listStockOut(this.queryParams).then((response) => {
        this.stockOutList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 加载库存信息
    loadPurchase() {
      this.purchaseLoading = true;
      listInventoryGroup(this.purchaseQueryParams).then((response) => {
        this.purchaseList = response.rows;
        this.purchasetotal = response.total;
        this.purchaseLoading = false;
      });
    },

    // 加载采购单明细
    loadPurchaseDetail() {
      this.purchaseDetailLoading = true;
      listPurchase_detail(this.purchaseQueryParams).then((response) => {
        this.purchaseDetailList = response.rows;
        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    // 取消按钮
    cancel() {
      // 取消的时候，清除保存的existingIds
      this.existingIds = new Set();
      console.log("取消后的 existingIds:", this.existingIds);
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        stockOutNo: null,
        purchaseNo: null,
        purchaseNo: null,
        supplierId: null,
        supplierCode: null,
        supplierName: null,
        stockOutType: "OTHER_OUT",
        stockOutDate: null,
        stockOutState: "TO_BE_CREATED",
        qualityState: null,
        warehouseId: null,
        warehouseCode: null,
        warehouseName: null,
        materialId: null,
        materialCode: null,
        materialName: null,
        stockOutOntherType: null,
        remark: null,
        comId: null,
        delFlag: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        returnMethod: null,
        wmsStockOutDetailList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    handlePurchaseQuery() {
      this.purchaseQueryParams.pageNum = 1;
      this.purchaseQueryParams.pageNum = 10;
      this.loadPurchase();
    },
    handlePurchaseDetailQuery() {
      // 物料搜索点击
      this.purchaseQueryDetailParams.pageNum = 1;
      listPurchase_detail(this.purchaseQueryDetailParams).then((response) => {
        this.purchaseDetailList = response.rows;
        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.queryParams.stockOutState = null;
      this.queryParams.stockOutDate = null;
      this.queryParams.stockOutNo = null;
      this.handleQuery();
    },
    resetPurchaseQuery() {
      (this.purchaseQueryParams.materialCode = ""),
        (this.purchaseQueryParams.materialName = ""),
        this.handlePurchaseQuery();
    },
    resetPurchaseDetailQuery() {
      (this.purchaseQueryParams.purchaseNo = ""),
        (this.purchaseQueryDetailParams.partCode = ""),
        (this.purchaseQueryDetailParams.partName = ""),
        listPurchase_detail(this.purchaseQueryParams).then((response) => {
          this.purchaseDetailList = response.rows;

          this.purchaseDetailtotal = response.total;
          this.purchaseDetailLoading = false;
        });
    },
    // // 多选框选中数据
    // handleSelectionChange(selection) {
    //   this.ids = selection.map(item => item.id);
    //   // 获取选中项的ID数组

    //   this.single = selection.length !== 1;
    //   this.multiple = !selection.length;
    //   // 根据ID筛选需要提交的数据

    //   // this.selectedPurchaseDetailList = this.purchaseDetailList.filter((item) =>
    //   //   this.ids.includes(item.id)
    //   // );

    //   // this.single = selection.length !== 1;
    //   // this.multiple = !selection.length;
    // },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.form.wmsStockOutDetailList = [];
      this.open = true;
      this.isPurchaseOrderDisabled = false;
      this.title = "新增其他出库单";
      console.log("this.isPurchaseOrderDisabled", this.isPurchaseOrderDisabled);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // 让输入框能够输入
      this.isPurchaseOrderDisabled = false;
      // 修改的时候先去清除保存的existingIds
      this.existingIds = new Set();
      console.log("修改清空的 existingIds:", this.existingIds);

      this.reset();
      const id = row.id || this.ids;
      getStockOut(id).then((response) => {
        this.form = response.data;
        this.form.wmsStockOutDetailList = response.data.wmsStockOutDetailList;
        this.open = true;
        this.title = "修改其他出库";
      });
    },

    //完成录入的时候的更新方法
    handlecomplete(row) {
      this.$modal
        .confirm('是否确认录入编号为"' + row.stockOutNo + '"的数据项？')
        .then(function () {})
        .then(() => {
          getStockOut(row.id).then((response) => {
            this.form = response.data;
            this.form.stockOutState = "STOCK_OUT_PENDING";
            updateStockOut(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.getList();
            });
          });
        })
        .catch(() => {});
    },

    /* 查看详情 */
    handleDatail(row) {
      this.reset();
      this.activeName = "first";
      this.stock_out_id = row.id;
      const id = row.id || this.ids;
      getStockOut(id).then((response) => {
        this.form = response.data;
        this.form.wmsStockOutDetailList = response.data.wmsStockOutDetailList;
        this.openDetail = true;
        this.detailTitle = "其他出库单详情";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          // 新增校验：检查明细表是否有数据
          if (
            !this.form.wmsStockOutDetailList ||
            this.form.wmsStockOutDetailList.length === 0
          ) {
            this.$message.error("请至少添加一条物料明细");
            return;
          }
          // this.form.wmsStockOutDetailList = this.wmsStockOutDetailList;
          if (this.form.id != null) {
            updateStockOut(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            // this.form.
            // this.form.
            addStockOut(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 作废按钮操作 */
    handlecancellation(row) {
      this.$modal
        .confirm('是否确认作废编号为"' + row.stockOutNo + '"的数据项？')
        .then(function () {})
        .then(() => {
          getStockOut(row.id).then((response) => {
            this.form = response.data;
            this.form.stockOutState = "TO_BE_CANCELLATION";
            updateStockOut(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.getList();
            });
          });
        })
        .catch(() => {});
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal
        .confirm('是否确认删除出库单主编号为"' + ids + '"的数据项？')
        .then(function () {
          return delStockOut(ids);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 出库单明细序号 */
    rowWmsStockOutDetailIndex({ row, rowIndex }) {
      row.index = rowIndex + 1;
    },
    /** 出库单明细添加按钮操作 */
    // handleAddWmsStockOutDetail() {
    //   let obj = {};
    //   obj.purchaseDetailId = "";
    //   obj.materialId = "";
    //   obj.materialCode = "";
    //   obj.materialName = "";
    //   obj.specification = "";
    //   obj.materialUnit = "";
    //   obj.qty = "";
    //   obj.incomingQty = "";
    //   obj.stockOutLine = "";
    //   obj.stockOutState = "";
    //   obj.qualityState = "";
    //   obj.qualifiedQty = "";
    //   obj.batchNo = "";
    //   obj.remark = "";
    //   obj.comId = "";
    //   obj.stockOutNo = "";
    //   this.form.wmsStockOutDetailList.push(obj);
    // },
    /** 出库单明细删除按钮操作 */
    handleDeleteWmsStockOutDetail() {
      if (this.checkedWmsStockOutDetail.length == 0) {
        this.$modal.msgError("请先选择要删除的出库单明细数据");
      } else {
        // const wmsStockOutDetailList = this.wmsStockOutDetailList;
        const wmsStockOutDetailList = this.form.wmsStockOutDetailList;
        const checkedWmsStockOutDetail = this.checkedWmsStockOutDetail;
        // 1. 收集被删除项的 ID（根据选中的 index）
        const deletedIds = checkedWmsStockOutDetail
          .map((index) => wmsStockOutDetailList[index - 1]?.id) // 根据 index 获取 id index的要从0开始，所以这里要-1！！
          .filter((id) => id !== undefined); // 过滤无效值
        console.log("被删除的 ID:", deletedIds);

        this.form.wmsStockOutDetailList = wmsStockOutDetailList.filter(
          (item) => {
            return checkedWmsStockOutDetail.indexOf(item.index) === -1;
          }
        );
        // 这里是Set
        deletedIds.forEach((id) => this.existingIds.delete(id));
        console.log("更新后的 existingIds:", this.existingIds);
        // 清空选中状态
        this.checkedWmsStockOutDetail = [];
        // 去判断是否数据等于0条，然后去取消disabled采购订单框
        if (this.form.wmsStockOutDetailList.length === 0) {
          this.isPurchaseOrderDisabled = false; // 可选重置
        }
      }
    },
    // /** 复选框选中数据 */
    // handleWmsStockOutDetailSelectionChange(selection) {
    //   this.checkedWmsStockOutDetail = selection.map(item => item.index);
    // },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "system/stockOut/export",
        {
          ...this.queryParams,
        },
        `出库单主_${new Date().toLocaleDateString()}.xlsx`
      );
    },
    receiveDetailData(data) {
      this.stock_detail_id = data;
    },
    // 标签点击
    handleClick() {
      this.stock_detail_id = null;
    },
    importFuction(event) {
      // 重置采购单明细搜索的数据
      this.purchaseDetailList = [];
      this.purchaseDetailtotal = 0;
      // 获取采购单，用于后面判断
      this.purchaseInputValue = event.target.value; // 直接获取输入框的当前值
      console.log("purchaseInputValue输入框的值:", this.purchaseInputValue);

      event.target.blur(); // 输入框失焦后再打开抽屉
      this.purchaseDrawerVisible = true;
      this.purchaseTitle = "物料选择";
      this.loadPurchase();
    },
    handleDrawerClose() {
      // ​​焦点管理​​
      // 打开抽屉时强制输入框失焦，避免遮罩层无法点击。
      // 关闭抽屉时再次触发失焦，确保状态干净

      this.purchaseDrawerVisible = false;
      this.$refs.purchaseInput?.blur(); // 主动触发失焦
    },
    // 多选框选中数据
    handleSelectionChangePurchase(selection) {
      let purchaseNo;
      let newArr = [];
      if (selection.length > 1) {
        var newRows = selection.filter((it, index) => {
          if (index == selection.length - 1) {
            this.$refs.leftMultipleTable.toggleRowSelection(it, true); //这行可以不要
            return true;
          } else {
            this.$refs.leftMultipleTable.toggleRowSelection(it, false);
            return false;
          }
        });
        newArr = newRows;
        purchaseNo = newRows[0].id;
      } else {
        newArr = selection;
        purchaseNo = selection[0].id;
      }
      this.form.purchaseNo = newArr[0].purchaseNo;
      //查询条件是No,这样Id覆盖了No, this.form.purchaseNo = newArr[0].id;
      this.form.supplierId = newArr[0].supplierId;
      this.form.supplierCode = newArr[0].supplierCode;
      this.form.supplierName = newArr[0].supplierName;
      const params = {
        purchaseNo: this.form.purchaseNo,
      };

      listPurchase_detail(params).then((response) => {
        this.purchaseDetailList = response.rows;

        this.purchaseDetailtotal = response.total;
        this.purchaseDetailLoading = false;
      });
    },
    rowClick(row) {
      this.$refs.leftMultipleTable.toggleRowSelection(row, true); //有这个就够了，因为一旦勾选的内容有变化，那么就会触发selectItem(rows)这个函数
    },
    onSelectAll() {
      this.$refs.leftMultipleTable.clearSelection();
    },
    handleSelectionDetail(selection) {
      this.selectedPurchaseDetailList = selection;
      this.selectedItems = selection; // 直接保存完整数据对象
      // this.ids = selection.map((item) => item.id);

      // // 根据ID筛选需要提交的数据

      // this.selectedPurchaseDetailList = this.purchaseDetailList.filter((item) =>
      //   this.ids.includes(item.id)
      // );

      // 更新选择状态
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    handleClose() {
      // 重置一下左边勾选
      this.$refs?.leftMultipleTable?.clearSelection();
      this.purchaseDrawerVisible = false;
    },
    handleAddPurchaseDetail() {
      // 这里根据输入去判断是否为空，如果有数据就是修改
      if (this.purchaseInputValue !== "") {
        console.log("purchaseInputValue:", this.purchaseInputValue);
        this.form.wmsStockOutDetailList = [];
        console.log(
          "去修改的时候先清空this.form.wmsStockOutDetailList:",
          this.form.wmsStockOutDetailList
        );
      }
      this.selectedPurchaseDetailList.forEach((obj) => {
        console.log(obj, "shuzhi");
        // 检查当前 obj 的 id 是否已存在
        // 如果存在，就不去添加
        if (!this.existingIds.has(obj.id)) {
          // 赋值字段
          obj.materialId = obj.id;
          obj.materialCode = obj.materialCode;
          obj.materialName = obj.materialName;
          obj.specification = obj.specification;
          obj.materialUnit = obj.materialUnit;
          console.log(obj, "obj---------");
          // 推送数据到目标数组
          this.form.wmsStockOutDetailList.push(obj);
          console.log(
            this.form.wmsStockOutDetailList,
            "this.form.wmsStockOutDetailList"
          );
          // 标记该 id 已存在
          this.existingIds.add(obj.id);
        }
      });
      this.purchaseDrawerVisible = false;
      // 去disabled采购订单框
      this.isPurchaseOrderDisabled = this.form.wmsStockOutDetailList.length > 0;

      this.$refs?.leftMultipleTable?.clearSelection();
      this.$refs?.rightMultipleTable?.clearSelection();
      // this.form.purchaseNo =
      //   this.selectedPurchaseDetailList[0].purchaseNo;
    },
  },
};
</script>
<style scoped>
.dropdown-container {
  position: relative;
  display: inline-block;
}

.ellipsis-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2em;
  padding: 0 8px;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  z-index: 100;
  display: flex;
  flex-direction: column;
  min-width: 100px;
}

.dropdown-menu button {
  padding: 8px 12px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
}

.dropdown-menu button:hover {
  background-color: #f5f5f5;
}
/* 栅格布局调整 */
.el-row {
  height: 50%;
}

/* 左侧表单样式 */
.left-form {
  height: 100%;
  overflow-y: auto;
  /* padding-right: 10px; */
}

/* 右侧表单样式 */
.right-form {
  height: 100%;
  overflow-y: auto;
  /* padding-left: 10px; */
}

/* 表单项间距调整 */
.el-form-item {
  margin-bottom: 12px;
}
/* 添加样式确保错误信息完整显示 */
.qty-form-item {
  margin-bottom: 30px; /* 为错误信息留出足够空间 */
}

/* 确保错误信息完整显示 */
.qty-form-item {
  margin-bottom: 30px;
}

/* 调整错误信息样式 */
.qty-form-item >>> .el-form-item__error {
  position: static; /* 确保错误信息显示在正确位置 */
  padding-top: 2px;
  font-size: 12px;
  line-height: 1.5;
  white-space: nowrap;
  color: #f56c6c;
}
/* 错误提示样式 */
.error-tip {
  color: #f56c6c;
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: relative;
  left: 0;
  top: -5px;
}
</style>