import request from '@/utils/request'

// 查询工单管理列表
export function listTrcOrderWork(query) {
  return request({
    url: '/mes/trcOrderWork/list',
    method: 'get',
    params: query
  })
}

// 查询工单管理详细
export function getTrcOrderWork(id) {
  return request({
    url: '/mes/trcOrderWork/' + id,
    method: 'get'
  })
}

// 新增工单管理
export function addTrcOrderWork(data) {
  return request({
    url: '/mes/trcOrderWork',
    method: 'post',
    data: data
  })
}

// 修改工单管理
export function updateTrcOrderWork(data) {
  return request({
    url: '/mes/trcOrderWork',
    method: 'put',
    data: data
  })
}

// 删除工单管理
export function delTrcOrderWork(id) {
  return request({
    url: '/mes/trcOrderWork/' + id,
    method: 'delete'
  })
}
// 拆分工单
export function splitTrcOrderWork(data) {
  return request({
    url: '/mes/trcOrderWork/split',
    method: 'put',
    data: data
  })
}
// 查询工单管理详细
export function getTrcOrderWorkByNo(woNo) {
  return request({
    url: '/mes/trcOrderWork/get/' + woNo,
    method: 'get'
  })
}
export function MergeTrcOrderWork(data) {
  return request({
    url: '/mes/trcOrderWork/Merge',
    method: 'put',
    data: data
  })
}
